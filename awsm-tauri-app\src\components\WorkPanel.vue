<template>
  <div class="work-panel" :class="{ 'maximized': isMaximized, 'normal': !isMaximized }">
    <!-- 主容器背景 -->
    <div class="panel-background"></div>

    <!-- 顶部分隔线 -->
    <div class="top-separator"></div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <span class="document-title">{{ documentTitle }}</span>
      </div>
      
      <div class="toolbar-right">
        <span class="file-path">{{ displayFilePath }}</span>
        <div class="toolbar-buttons">
          <button class="action-button" @click="openDocument" title="打开文档">
            打开
          </button>
          <button class="action-button" @click="saveDocument" title="保存文档">
            保存
          </button>
          <button class="action-button" @click="exportDocument" title="导出文档">
            导出
          </button>
        </div>
      </div>
    </div>

    <!-- 内容编辑区域 -->
    <div class="content-area">
      <div class="editor-container">
        <textarea
          ref="editorTextarea"
          v-model="documentContent"
          class="content-editor"
          placeholder="开始编写您的内容..."
          @input="handleContentChange"
          @scroll="handleEditorScroll"
        ></textarea>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span class="word-count">字数: {{ wordCount }}</span>
        <span class="char-count">字符: {{ charCount }}</span>
      </div>
      <div class="status-right">
        <span class="cursor-position">行 {{ cursorLine }}, 列 {{ cursorColumn }}</span>
        <span class="encoding">UTF-8</span>
      </div>
    </div>

    <!-- 自定义滚动条 -->
    <div class="custom-scrollbar" v-show="showScrollbar">
      <div
        class="scrollbar-track"
        @click="handleTrackClick"
        ref="scrollbarTrack"
      >
        <div
          class="scrollbar-thumb"
          :style="{
            height: thumbHeight + 'px',
            top: thumbTop + 'px'
          }"
          @mousedown="startDrag"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { getCurrentWindow } from '@tauri-apps/api/window';
// import { save, open } from '@tauri-apps/api/dialog';
// import { writeTextFile, readTextFile } from '@tauri-apps/api/fs';

// 响应式数据
const isMaximized = ref(false);
const documentTitle = ref('未命名文档');
const documentContent = ref('');

const cursorLine = ref(1);
const cursorColumn = ref(1);
const fullFilePath = ref('');
const currentFileName = ref('');

// 滚动条相关
const showScrollbar = ref(false);
const thumbHeight = ref(50);
const thumbTop = ref(0);
const isDragging = ref(false);
const dragStartY = ref(0);
const dragStartTop = ref(0);

// 引用
const editorTextarea = ref<HTMLTextAreaElement>();
const scrollbarTrack = ref<HTMLElement>();

// 计算属性
const wordCount = computed(() => {
  return documentContent.value.replace(/\s+/g, '').length;
});

const charCount = computed(() => {
  return documentContent.value.length;
});

const displayFilePath = computed(() => {
  if (!fullFilePath.value) {
    return '未保存';
  }

  // 只显示文件名，不显示完整路径
  const pathParts = fullFilePath.value.split(/[/\\]/);
  return pathParts[pathParts.length - 1] || '未命名文档';
});

// 方法
const newDocument = () => {
  if (documentContent.value && !confirm('确定要新建文档吗？未保存的内容将丢失。')) {
    return;
  }
  documentContent.value = '';
  documentTitle.value = '未命名文档';
  fullFilePath.value = '';
  currentFileName.value = '';
};

const openDocument = async () => {
  console.log('打开文档功能暂未实现');
  // TODO: 实现文档打开功能
};

const saveDocument = async () => {
  console.log('保存文档功能暂未实现');
  // TODO: 实现文档保存功能
};

const exportDocument = async () => {
  console.log('导出文档功能暂未实现');
  // TODO: 实现文档导出功能
};



const handleContentChange = () => {
  updateCursorPosition();
  nextTick(() => {
    updateScrollbar();
  });
};

const updateCursorPosition = () => {
  if (!editorTextarea.value) return;
  
  const textarea = editorTextarea.value;
  const cursorPos = textarea.selectionStart;
  const textBeforeCursor = textarea.value.substring(0, cursorPos);
  const lines = textBeforeCursor.split('\n');
  
  cursorLine.value = lines.length;
  cursorColumn.value = lines[lines.length - 1].length + 1;
};

// 滚动条相关方法
const updateScrollbar = () => {
  if (!editorTextarea.value) return;
  
  const textarea = editorTextarea.value;
  const scrollHeight = textarea.scrollHeight;
  const clientHeight = textarea.clientHeight;
  
  if (scrollHeight <= clientHeight) {
    showScrollbar.value = false;
    return;
  }
  
  showScrollbar.value = true;
  const scrollRatio = clientHeight / scrollHeight;
  const trackHeight = clientHeight - 20;
  
  thumbHeight.value = Math.max(20, trackHeight * scrollRatio);
  
  const scrollTop = textarea.scrollTop;
  const maxScrollTop = scrollHeight - clientHeight;
  const scrollPercentage = maxScrollTop > 0 ? scrollTop / maxScrollTop : 0;
  const maxThumbTop = trackHeight - thumbHeight.value;
  
  thumbTop.value = Math.min(maxThumbTop, scrollPercentage * maxThumbTop);
};

const handleEditorScroll = () => {
  updateScrollbar();
};

const handleTrackClick = (event: MouseEvent) => {
  if (!scrollbarTrack.value || !editorTextarea.value) return;
  
  const rect = scrollbarTrack.value.getBoundingClientRect();
  const clickY = event.clientY - rect.top;
  const trackHeight = rect.height;
  const percentage = clickY / trackHeight;
  
  const textarea = editorTextarea.value;
  const maxScrollTop = textarea.scrollHeight - textarea.clientHeight;
  textarea.scrollTop = percentage * maxScrollTop;
  
  updateScrollbar();
};

const startDrag = (event: MouseEvent) => {
  isDragging.value = true;
  dragStartY.value = event.clientY;
  dragStartTop.value = thumbTop.value;
  
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
  event.preventDefault();
};

const handleDrag = (event: MouseEvent) => {
  if (!isDragging.value || !scrollbarTrack.value || !editorTextarea.value) return;
  
  const deltaY = event.clientY - dragStartY.value;
  const newTop = dragStartTop.value + deltaY;
  const trackHeight = scrollbarTrack.value.clientHeight;
  const maxTop = trackHeight - thumbHeight.value;
  
  const clampedTop = Math.max(0, Math.min(maxTop, newTop));
  const percentage = maxTop > 0 ? clampedTop / maxTop : 0;
  
  const textarea = editorTextarea.value;
  const maxScrollTop = textarea.scrollHeight - textarea.clientHeight;
  textarea.scrollTop = percentage * maxScrollTop;
  
  updateScrollbar();
};

const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 窗口状态检查
const checkMaximizedState = async () => {
  try {
    const window = getCurrentWindow();
    const wasMaximized = isMaximized.value;
    isMaximized.value = await window.isMaximized();
    
    if (wasMaximized !== isMaximized.value) {
      setTimeout(() => {
        updateScrollbar();
      }, 100);
    }
  } catch (error) {
    console.error('检查窗口状态失败:', error);
  }
};

onMounted(async () => {
  await checkMaximizedState();

  setTimeout(() => {
    updateScrollbar();
  }, 100);

  // 初始化示例文档
  setTimeout(() => {
    documentContent.value = `# 示例文档

这是一个示例文档，展示工作面板的编辑功能。

## 功能特性

1. **文本编辑** - 支持多行文本编辑
2. **预览模式** - 可以切换到预览模式查看格式化内容
3. **文件管理** - 支持新建、保存、导出文档
4. **撤销重做** - 支持编辑历史管理
5. **状态显示** - 实时显示字数、字符数、光标位置

## 使用说明

在这里开始编写您的内容...`;

    // 设置示例文件路径
    fullFilePath.value = 'E:\\Documents\\Projects\\创作项目\\示例文档.txt';
    currentFileName.value = '示例文档.txt';
    documentTitle.value = '示例文档';

    updateScrollbar();
  }, 500);

  const window = getCurrentWindow();
  const unlisten = await window.onResized(() => {
    checkMaximizedState();
    updateScrollbar();
  });

  onUnmounted(() => {
    unlisten();
    document.removeEventListener('mousemove', handleDrag);
    document.removeEventListener('mouseup', stopDrag);
  });
});
</script>

<style scoped>
.work-panel {
  position: absolute;
  transition: all 0.3s ease;
  overflow: hidden;
}

/* 最大化状态 */
.work-panel.maximized {
  top: 58px;
  left: 920px; /* 参数面板右侧 */
  width: 958px;
  height: 977px;
}

/* 普通状态 - 保持与最大化状态相同的布局比例 */
.work-panel.normal {
  top: 40px;
  left: 635px; /* 参数面板右侧，按比例缩放 */
  width: 661px; /* 958px * 0.69 ≈ 661px */
  height: 674px; /* 977px * 0.69 ≈ 674px */
}

.panel-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.07) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: 20px;
  backdrop-filter: blur(80px);
  -webkit-backdrop-filter: blur(80px);
}

.top-separator {
  position: absolute;
  top: 65.5px;
  left: 1px;
  right: 1px;
  height: 1.2px;
  background: rgba(255, 255, 255, 0.09);
}

.toolbar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 65px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 10;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}

.toolbar-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-button {
  padding: 6px 12px;
  background: rgba(13, 255, 0, 0.1);
  border: 1px solid rgba(13, 255, 0, 0.3);
  border-radius: 4px;
  color: rgba(13, 255, 0, 0.9);
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.action-button:hover {
  background: rgba(13, 255, 0, 0.2);
  border-color: rgba(13, 255, 0, 0.5);
  color: rgba(13, 255, 0, 1);
  box-shadow: 0 0 8px rgba(13, 255, 0, 0.3);
}

.action-button:active {
  background: rgba(13, 255, 0, 0.3);
  transform: translateY(1px);
}

.file-path {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(0, 0, 0, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  white-space: nowrap;
  cursor: default;
  transition: all 0.2s ease;
}

.file-path:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}



.document-title {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

.content-area {
  position: absolute;
  top: 67px;
  left: 20px;
  right: 20px;
  bottom: 40px;
  overflow: hidden;
}

.editor-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.content-editor {
  width: 100%;
  height: 100%;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.content-editor::-webkit-scrollbar {
  display: none;
}

.content-editor::placeholder {
  color: rgba(255, 255, 255, 0.4);
}



.status-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background: rgba(0, 0, 0, 0.1);
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 0 0 20px 20px;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-bar span {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

/* 自定义滚动条 */
.custom-scrollbar {
  position: absolute;
  top: 87px;
  right: 25px;
  bottom: 60px;
  width: 8px;
  z-index: 20;
}

.scrollbar-track {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  position: relative;
  cursor: pointer;
}

.scrollbar-thumb {
  position: absolute;
  left: 0;
  width: 100%;
  background: rgba(13, 255, 0, 0.6);
  border-radius: 4px;
  cursor: grab;
  transition: background-color 0.2s ease;
}

.scrollbar-thumb:hover {
  background: rgba(13, 255, 0, 0.8);
}

.scrollbar-thumb:active {
  cursor: grabbing;
  background: rgba(13, 255, 0, 1);
}

/* 普通状态下的样式调整 */
.work-panel.normal .toolbar {
  height: 45px;
  padding: 0 14px;
}

.work-panel.normal .document-title {
  font-size: 12px;
}

.work-panel.normal .top-separator {
  top: 45px;
}

.work-panel.normal .content-area {
  top: 47px;
  left: 14px;
  right: 14px;
  bottom: 28px;
}

.work-panel.normal .content-editor {
  font-size: 12px;
  padding: 14px;
}

.work-panel.normal .status-bar {
  height: 28px;
  padding: 0 14px;
}

.work-panel.normal .status-bar span {
  font-size: 9px;
}

.work-panel.normal .custom-scrollbar {
  top: 60px;
  right: 18px;
  bottom: 42px;
  width: 6px;
}

.work-panel.normal .file-path {
  font-size: 9px;
  padding: 3px 6px;
}

.work-panel.normal .action-button {
  padding: 4px 8px;
  font-size: 9px;
}

.work-panel.normal .toolbar-buttons {
  gap: 6px;
}

.work-panel.normal .toolbar-right {
  gap: 8px;
}
</style>
