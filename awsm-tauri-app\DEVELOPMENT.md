# 开发指南

## 项目结构

```
awsm-tauri-app/
├── src/                  # Vue 前端代码
├── src-tauri/            # Rust 后端代码
│   ├── src/
│   │   ├── main.rs       # 主入口点
│   │   └── lib.rs        # 应用逻辑
│   ├── Cargo.toml        # Rust 依赖配置
│   └── tauri.conf.json   # Tauri 配置
├── scripts/              # 开发脚本
└── package.json          # Node.js 依赖和脚本
```

## 开发流程

### 启动开发环境

1. 确保已安装必要的依赖：
   ```
   npm install
   ```

2. 启动开发服务器：
   ```
   npm run tauri:dev
   ```
   
   或者使用脚本：
   - Windows: `scripts/start-dev.bat`
   - PowerShell: `scripts/start-dev.ps1`

### 构建应用

构建应用以进行分发：
```
npm run tauri:build
```

## 代码组织

### 前端 (Vue 3 + TypeScript)

- `src/App.vue`: 主应用组件
- `src/main.ts`: Vue 应用入口点

### 后端 (Rust)

- `src-tauri/src/main.rs`: Tauri 应用主入口点
- `src-tauri/src/lib.rs`: 应用逻辑和 Tauri 命令

## Tauri 命令

Tauri 命令是在 Rust 中定义的函数，可以通过前端 JavaScript 调用。

### 示例命令

```rust
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}
```

在前端调用：
```typescript
import { invoke } from "@tauri-apps/api/core";

const result = await invoke("greet", { name: "World" });
```

## 配置文件

### `tauri.conf.json`

Tauri 应用的主要配置文件，包含应用标识、窗口设置、构建选项等。

### `Cargo.toml`

Rust 项目的依赖和配置。

## 故障排除

### 端口冲突

如果遇到端口冲突错误，请检查 `vite.config.ts` 和 `tauri.conf.json` 中的端口设置。

### Android 开发

要进行 Android 开发，需要：
1. 安装 Android SDK
2. 设置 `ANDROID_HOME` 环境变量
3. 初始化 Android 环境：
   ```
   npm run tauri android init
