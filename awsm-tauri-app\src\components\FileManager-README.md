# FileManager 组件

基于 Figma 设计实现的文件管理器组件，具有现代化的玻璃态效果和完整的文件树功能。

## 功能特性

### 🎨 视觉设计
- **玻璃态效果**: 匹配 Figma 设计的渐变背景和模糊效果
- **响应式布局**: 支持展开/收起状态切换
- **现代化图标**: SVG 图标，支持不同文件类型的颜色区分
- **自定义滚动条**: 与应用整体风格一致的滚动条设计

### 📁 文件管理功能
- **文件树显示**: 层级化显示文件夹和文件
- **展开/收起**: 点击文件夹可展开查看子内容
- **文件选择**: 支持单击选择文件，双击打开文件
- **文件类型识别**: 根据文件扩展名显示不同颜色的图标
- **文件大小显示**: 显示文件大小信息

### 🔧 交互功能
- **刷新按钮**: 重新加载当前目录内容
- **打开目录**: 在系统文件管理器中打开当前目录
- **选择文件夹**: 选择新的工作目录
- **键盘导航**: 支持键盘操作（待实现）

## 组件结构

```
FileManager.vue          # 主文件管理器组件
├── FileTreeNode.vue     # 文件树节点组件
└── FileManager-README.md # 说明文档
```

## 使用方法

### 基本使用

```vue
<template>
  <FileManager @fileSelect="handleFileSelect" />
</template>

<script setup>
import FileManager from './components/FileManager.vue';

const handleFileSelect = (file) => {
  console.log('选中文件:', file);
  // 处理文件选择逻辑
};
</script>
```

### 事件

| 事件名 | 参数 | 描述 |
|--------|------|------|
| `fileSelect` | `FileItem` | 当用户选择文件时触发 |

### FileItem 接口

```typescript
interface FileItem {
  name: string;          // 文件/文件夹名称
  path: string;          // 完整路径
  isDirectory: boolean;  // 是否为文件夹
  children?: FileItem[]; // 子项（仅文件夹）
  isExpanded?: boolean;  // 是否展开（仅文件夹）
  size?: number;         // 文件大小（仅文件）
  modified?: Date;       // 修改时间
}
```

## 样式特性

### 设计规范
- **尺寸**: 264x431px（展开状态）
- **背景**: 线性渐变 + 80px 模糊效果
- **边框**: 圆角 0px 15px 15px 0px
- **颜色**: 主题色为绿色 (#0DFF00)

### 状态样式
- **收起状态**: 仅显示展开按钮（24x21px）
- **展开状态**: 显示完整文件管理器界面
- **悬停效果**: 按钮和文件项的悬停反馈
- **选中状态**: 绿色边框和背景高亮

## 技术实现

### 核心技术
- **Vue 3**: Composition API + TypeScript
- **Tauri**: 跨平台桌面应用框架
- **CSS**: 玻璃态效果和动画

### 文件系统集成
目前使用模拟数据，实际项目中可以集成：
- `@tauri-apps/api/dialog` - 文件夹选择对话框
- `@tauri-apps/api/fs` - 文件系统操作
- `@tauri-apps/api/shell` - 系统命令执行

### 性能优化
- **虚拟滚动**: 大量文件时的性能优化（待实现）
- **懒加载**: 文件夹内容按需加载
- **防抖处理**: 避免频繁的文件系统操作

## 扩展功能

### 计划中的功能
- [ ] 文件搜索和过滤
- [ ] 文件拖拽操作
- [ ] 右键菜单（复制、粘贴、删除等）
- [ ] 文件预览功能
- [ ] 收藏夹和快速访问
- [ ] 文件操作历史记录

### 自定义配置
- [ ] 主题颜色配置
- [ ] 文件类型图标自定义
- [ ] 显示选项（隐藏文件、排序方式等）

## 注意事项

1. **Tauri 权限**: 需要在 `tauri.conf.json` 中配置文件系统访问权限
2. **跨平台兼容**: 路径分隔符在不同系统中的处理
3. **性能考虑**: 大型目录结构的加载和渲染优化
4. **错误处理**: 文件访问权限和异常情况的处理

## 更新日志

### v1.0.0 (2025-07-30)
- ✅ 基础文件管理器界面
- ✅ 玻璃态效果设计
- ✅ 文件树展示功能
- ✅ 展开/收起动画
- ✅ 文件类型图标
- ✅ 自定义滚动条
- ✅ 响应式布局
