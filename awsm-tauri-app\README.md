# awsm-tauri-app

一个使用 Tauri 2、Vue 3、TypeScript 和 Rust 构建的跨平台应用。

## 功能

- 桌面端支持 (Windows)
- 移动端支持 (Android)
- 使用 Vue 3 构建的现代前端界面
- 使用 Rust 编写的高性能后端逻辑
- TypeScript 类型安全

## 技术栈

- [Tauri 2](https://tauri.app/)
- [Vue 3](https://vuejs.org/)
- [TypeScript](https://www.typescriptlang.org/)
- [Rust](https://www.rust-lang.org/)

## 开发环境要求

- [Rust](https://www.rust-lang.org/)
- [Node.js](https://nodejs.org/) (推荐使用最新 LTS 版本)
- [Tauri CLI](https://tauri.app/v1/guides/getting-started/prerequisites/)

对于 Android 开发，还需要：
- Android SDK
- 设置 `ANDROID_HOME` 环境变量

## 快速开始

1. 安装依赖：
   ```bash
   npm install
   ```

2. 启动开发服务器：
   ```bash
   npm run tauri:dev
   ```

## 构建应用

构建应用以进行分发：
```bash
npm run tauri:build
```

## 项目结构

```
awsm-tauri-app/
├── src/                  # Vue 前端代码
├── src-tauri/            # Rust 后端代码
│   ├── src/
│   │   ├── main.rs       # 主入口点
│   │   └── lib.rs        # 应用逻辑
│   ├── Cargo.toml        # Rust 依赖配置
│   └── tauri.conf.json   # Tauri 配置
├── scripts/              # 开发脚本
└── package.json          # Node.js 依赖和脚本
```

## 更多信息

请查看 [DEVELOPMENT.md](DEVELOPMENT.md) 获取详细的开发指南。
