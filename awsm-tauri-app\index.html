<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tauri + Vue + Typescript App</title>
    <style>
      html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        background-color: #000000 !important;
      }
      #app {
        width: 100%;
        height: 100%;
        background-color: #000000 !important;
      }
    </style>
  </head>

  <body style="background-color: #000000 !important;">
    <div id="app" style="background-color: #000000 !important;"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
