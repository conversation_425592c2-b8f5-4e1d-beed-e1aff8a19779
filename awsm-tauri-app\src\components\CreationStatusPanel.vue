<template>
  <div class="creation-status-panel" :class="{ 'maximized': isMaximized, 'normal': !isMaximized }">
    <!-- 状态指示器 -->
    <div class="status-indicator">
      <div class="status-dot" :class="statusType"></div>
      <span class="status-text">{{ statusText }}</span>
    </div>

    <!-- 内容区域 -->
    <div class="panel-content" @scroll="handleContentScroll">
      <div class="status-content">
        <div v-if="creationLogs.length === 0" class="empty-state">
          <p>等待开始创作...</p>
        </div>
        <div v-else class="log-container">
          <div 
            v-for="(log, index) in creationLogs" 
            :key="index" 
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义滚动条 -->
    <div class="custom-scrollbar" v-show="showScrollbar">
      <div 
        class="scrollbar-track" 
        @click="handleTrackClick"
        ref="scrollbarTrack"
      >
        <div 
          class="scrollbar-thumb" 
          :style="{ 
            height: thumbHeight + 'px', 
            top: thumbTop + 'px' 
          }"
          @mousedown="startDrag"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, nextTick } from 'vue';
import { getCurrentWindow } from '@tauri-apps/api/window';

// 窗口最大化状态
const isMaximized = ref(false);

// 滚动条相关状态
const showScrollbar = ref(false);
const thumbHeight = ref(50);
const thumbTop = ref(0);
const isDragging = ref(false);
const dragStartY = ref(0);
const dragStartScrollTop = ref(0);

// DOM引用
const scrollbarTrack = ref<HTMLElement | null>(null);

// 创作状态
const statusText = ref('系统运行中');
const statusType = ref('running'); // running, idle, complete, error

// 创作日志
interface CreationLog {
  timestamp: Date;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
}

const creationLogs = reactive<CreationLog[]>([]);

// 添加日志
const addLog = (message: string, type: CreationLog['type'] = 'info') => {
  creationLogs.push({
    timestamp: new Date(),
    message,
    type
  });
  
  // 自动滚动到底部
  nextTick(() => {
    const contentEl = getContentElement();
    if (contentEl) {
      contentEl.scrollTop = contentEl.scrollHeight;
    }
    updateScrollbar();
  });
};

// 格式化时间
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-CN', { 
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 更新状态
const updateStatus = (text: string, type: string = 'running') => {
  statusText.value = text;
  statusType.value = type;
};

// 获取内容元素
const getContentElement = (): HTMLElement | null => {
  return document.querySelector('.creation-status-panel .panel-content');
};

// 更新滚动条状态
const updateScrollbar = () => {
  nextTick(() => {
    const contentEl = getContentElement();
    if (!contentEl) return;

    const { scrollTop, scrollHeight, clientHeight } = contentEl;
    
    // 检查是否需要显示滚动条
    showScrollbar.value = scrollHeight > clientHeight;
    
    if (showScrollbar.value && scrollbarTrack.value) {
      const trackHeight = scrollbarTrack.value.clientHeight;
      
      // 计算滚动条拇指的高度
      const thumbHeightRatio = clientHeight / scrollHeight;
      thumbHeight.value = Math.max(trackHeight * thumbHeightRatio, 20); // 最小高度20px
      
      // 确保拇指高度不超过轨道高度
      thumbHeight.value = Math.min(thumbHeight.value, trackHeight);
      
      // 计算滚动条拇指的位置
      const maxScrollTop = scrollHeight - clientHeight;
      const scrollRatio = maxScrollTop > 0 ? scrollTop / maxScrollTop : 0;
      const maxThumbTop = trackHeight - thumbHeight.value;
      
      // 确保拇指位置不超出轨道范围
      thumbTop.value = Math.max(0, Math.min(scrollRatio * maxThumbTop, maxThumbTop));
    }
  });
};

// 处理内容滚动
const handleContentScroll = () => {
  if (!isDragging.value) {
    updateScrollbar();
  }
};

// 处理轨道点击
const handleTrackClick = (event: MouseEvent) => {
  const contentEl = getContentElement();
  if (!contentEl || !scrollbarTrack.value) return;

  const trackRect = scrollbarTrack.value.getBoundingClientRect();
  const clickY = event.clientY - trackRect.top;
  const trackHeight = scrollbarTrack.value.clientHeight;
  
  // 计算点击位置对应的滚动比例
  const clickRatio = clickY / trackHeight;
  const maxScrollTop = contentEl.scrollHeight - contentEl.clientHeight;
  
  // 设置滚动位置
  contentEl.scrollTop = clickRatio * maxScrollTop;
  updateScrollbar();
};

// 开始拖拽
const startDrag = (event: MouseEvent) => {
  const contentEl = getContentElement();
  if (!contentEl) return;

  isDragging.value = true;
  dragStartY.value = event.clientY;
  dragStartScrollTop.value = contentEl.scrollTop;
  
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
  event.preventDefault();
};

// 处理拖拽
const handleDrag = (event: MouseEvent) => {
  if (!isDragging.value || !scrollbarTrack.value) return;
  
  const contentEl = getContentElement();
  if (!contentEl) return;

  const deltaY = event.clientY - dragStartY.value;
  const trackHeight = scrollbarTrack.value.clientHeight;
  const maxScrollTop = contentEl.scrollHeight - contentEl.clientHeight;
  
  // 计算拖拽比例
  const dragRatio = deltaY / trackHeight;
  const newScrollTop = dragStartScrollTop.value + (dragRatio * maxScrollTop);
  
  // 限制滚动范围
  contentEl.scrollTop = Math.max(0, Math.min(newScrollTop, maxScrollTop));
  updateScrollbar();
};

// 停止拖拽
const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 检查窗口最大化状态
const checkMaximizedState = async () => {
  try {
    const window = getCurrentWindow();
    const wasMaximized = isMaximized.value;
    isMaximized.value = await window.isMaximized();
    
    // 如果窗口状态发生变化，重新计算滚动条
    if (wasMaximized !== isMaximized.value) {
      setTimeout(() => {
        updateScrollbar();
      }, 100); // 延迟一点时间等待CSS过渡完成
    }
  } catch (error) {
    console.error('检查窗口状态失败:', error);
  }
};

onMounted(async () => {
  await checkMaximizedState();

  // 初始化滚动条
  setTimeout(() => {
    updateScrollbar();
  }, 100);

  // 添加一些示例日志来展示不同状态
  setTimeout(() => {
    addLog('系统初始化完成', 'success');
    updateStatus('系统运行中', 'running');
  }, 500);

  setTimeout(() => {
    addLog('正在加载创作模块...', 'info');
  }, 1000);

  setTimeout(() => {
    addLog('创作引擎已启动', 'success');
  }, 1500);

  // 监听窗口状态变化
  const window = getCurrentWindow();
  const unlisten = await window.onResized(() => {
    checkMaximizedState();
    updateScrollbar();
  });

  // 组件卸载时清理监听器
  onUnmounted(() => {
    unlisten();
    stopDrag(); // 清理拖拽事件
  });
});

// 暴露方法供父组件调用
defineExpose({
  addLog,
  updateStatus
});
</script>

<style scoped>
/* 创作状态面板主容器 */
.creation-status-panel {
  position: absolute;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.07) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: 20px;
  backdrop-filter: blur(80px);
  -webkit-backdrop-filter: blur(80px);
  transition: all 0.3s ease;
  overflow: hidden;
}

/* 最大化状态 */
.creation-status-panel.maximized {
  top: 650px; /* 参数面板下方 */
  left: 113px;
  width: 774px;
  height: 377px;
}

/* 普通状态 - 保持与最大化状态相同的布局比例 */
.creation-status-panel.normal {
  top: 449px; /* 650px * 0.69 ≈ 449px */
  left: 78px; /* 113px * 0.69 ≈ 78px，与参数面板对齐 */
  width: 534px; /* 774px * 0.69 ≈ 534px，与参数面板宽度一致 */
  height: 260px; /* 377px * 0.69 ≈ 260px */
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 6.4%; /* 24px / 377px ≈ 6.4% */
  left: 2.5%; /* 19px / 774px ≈ 2.5% */
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  background-color: #0DFF00;
  border-radius: 50%;
  box-shadow: 0 0 6px rgba(13, 255, 0, 0.5);
  animation: pulse 2s infinite;
}

.status-dot.running {
  background-color: #0DFF00;
  box-shadow: 0 0 6px rgba(13, 255, 0, 0.5);
  animation: pulse 2s infinite;
}

.status-dot.idle {
  background-color: #888888;
  box-shadow: 0 0 6px rgba(136, 136, 136, 0.5);
  animation: none;
}

.status-dot.complete {
  background-color: #00FF88;
  box-shadow: 0 0 6px rgba(0, 255, 136, 0.5);
  animation: none;
}

.status-dot.error {
  background-color: #FF4444;
  box-shadow: 0 0 6px rgba(255, 68, 68, 0.5);
  animation: pulse-error 1.5s infinite;
}

/* 与参数面板相同的脉冲动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 6px rgba(13, 255, 0, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 12px rgba(13, 255, 0, 0.8);
    transform: scale(1.1);
  }
  100% {
    box-shadow: 0 0 6px rgba(13, 255, 0, 0.5);
    transform: scale(1);
  }
}

/* 错误状态动画 - 红色脉冲 */
@keyframes pulse-error {
  0% {
    box-shadow: 0 0 6px rgba(255, 68, 68, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 12px rgba(255, 68, 68, 0.8);
    transform: scale(1.1);
  }
  100% {
    box-shadow: 0 0 6px rgba(255, 68, 68, 0.5);
    transform: scale(1);
  }
}

.status-text {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.21;
  color: #FFFFFF;
  text-align: left;
}

/* 内容区域 */
.panel-content {
  position: absolute;
  top: 18%; /* 约68px / 377px */
  left: 2.5%;
  right: 2.5%;
  bottom: 3%;
  color: #FFFFFF;
  overflow-y: auto;
  padding: 0;
}

.panel-content::-webkit-scrollbar {
  display: none;
}

.panel-content {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.status-content {
  padding: 10px;
  height: 100%;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  opacity: 0.6;
}

.empty-state p {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: #FFFFFF;
  margin: 0;
}

.log-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
}

.log-item.info {
  border-left-color: #0DFF00;
}

.log-item.success {
  border-left-color: #00FF88;
}

.log-item.warning {
  border-left-color: #FFAA00;
}

.log-item.error {
  border-left-color: #FF4444;
}

.log-time {
  font-family: 'Courier New', monospace;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
  min-width: 60px;
  flex-shrink: 0;
}

.log-message {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 12px;
  color: #FFFFFF;
  line-height: 1.4;
}

/* 自定义滚动条 */
.custom-scrollbar {
  position: absolute;
  top: 18%;
  right: 1.4%;
  width: 8px;
  bottom: 5%;
  z-index: 10;
}

.scrollbar-track {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(138, 138, 138, 0.3);
  border: 1px solid rgba(138, 138, 138, 0.2);
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.scrollbar-track:hover {
  background: rgba(138, 138, 138, 0.4);
}

.scrollbar-thumb {
  position: absolute;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 4px;
  cursor: grab;
  transition: background-color 0.2s ease;
  min-height: 20px;
}

.scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.8);
}

.scrollbar-thumb:active {
  cursor: grabbing;
  background: rgba(13, 255, 0, 0.7);
}

/* 普通状态下的比例调整 - 保持与参数面板一致 */
.creation-status-panel.normal .status-dot {
  width: 6px; /* 与参数面板普通状态一致 */
  height: 6px;
}

.creation-status-panel.normal .status-text {
  font-size: 11px; /* 按比例缩放 */
}

.creation-status-panel.normal .empty-state p {
  font-size: 13px; /* 按比例缩放 */
}

.creation-status-panel.normal .log-time {
  font-size: 9px; /* 按比例缩放 */
  min-width: 50px;
}

.creation-status-panel.normal .log-message {
  font-size: 11px; /* 按比例缩放 */
  line-height: 1.4;
}

.creation-status-panel.normal .custom-scrollbar {
  width: 6px; /* 按比例缩放 */
}

.creation-status-panel.normal .log-item {
  padding: 6px 8px; /* 按比例缩放 */
  gap: 8px;
}
</style>
