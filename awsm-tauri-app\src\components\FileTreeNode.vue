<template>
  <div class="tree-node" :style="{ paddingLeft: (level * 16 + 8) + 'px' }">
    <div 
      class="node-content" 
      :class="{ 
        'selected': isSelected,
        'directory': item.isDirectory,
        'file': !item.isDirectory 
      }"
      @click="handleClick"
      @dblclick="handleDoubleClick"
    >
      <!-- 展开/收起图标 -->
      <div class="expand-icon" v-if="item.isDirectory" @click.stop="toggleExpanded">
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path 
            :d="item.isExpanded ? 'M3 4.5L6 7.5L9 4.5' : 'M4.5 3L7.5 6L4.5 9'" 
            stroke="rgba(255,255,255,0.6)" 
            stroke-width="1.5" 
            stroke-linecap="round" 
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="expand-placeholder" v-else></div>

      <!-- 文件/文件夹图标 -->
      <div class="file-icon">
        <!-- 文件夹图标 -->
        <svg v-if="item.isDirectory" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path 
            d="M10 6H5C3.89543 6 3 6.89543 3 8V18C3 19.1046 3.89543 20 5 20H19C20.1046 20 21 19.1046 21 18V10C21 8.89543 20.1046 8 19 8H12L10 6Z" 
            :fill="item.isExpanded ? 'rgba(13, 255, 0, 0.3)' : 'rgba(255, 193, 7, 0.3)'" 
            :stroke="item.isExpanded ? 'rgba(13, 255, 0, 0.8)' : 'rgba(255, 193, 7, 0.8)'" 
            stroke-width="1"
          />
        </svg>
        
        <!-- 文件图标 -->
        <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path 
            d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z" 
            :fill="getFileIconColor()" 
            :stroke="getFileIconStroke()" 
            stroke-width="1"
          />
          <path d="M14 2V8H20" :stroke="getFileIconStroke()" stroke-width="1" fill="none"/>
        </svg>
      </div>

      <!-- 文件名 -->
      <span class="file-name" :title="item.name">{{ item.name }}</span>

      <!-- 文件信息 -->
      <div class="file-info" v-if="!item.isDirectory && item.size">
        <span class="file-size">{{ formatFileSize(item.size) }}</span>
      </div>
    </div>

    <!-- 子节点 -->
    <div class="children" v-if="item.isDirectory && item.isExpanded && item.children">
      <FileTreeNode
        v-for="child in item.children"
        :key="child.path"
        :item="child"
        :level="level + 1"
        :selected-path="selectedPath"
        @select="$emit('select', $event)"
        @toggle="$emit('toggle', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// 接口定义
interface FileItem {
  name: string;
  path: string;
  isDirectory: boolean;
  children?: FileItem[];
  isExpanded?: boolean;
  size?: number;
  modified?: Date;
}

// Props
const props = defineProps<{
  item: FileItem;
  level: number;
  selectedPath?: string;
}>();

// Emits
const emit = defineEmits<{
  select: [file: FileItem];
  toggle: [folder: FileItem];
}>();

// 计算属性
const isSelected = computed(() => {
  return props.selectedPath === props.item.path;
});

// 方法
const handleClick = () => {
  emit('select', props.item);
};

const handleDoubleClick = () => {
  if (props.item.isDirectory) {
    toggleExpanded();
  } else {
    // 双击文件时的处理逻辑
    console.log('打开文件:', props.item.name);
    // 这里可以触发文件打开事件
    // emit('openFile', props.item);
  }
};

const toggleExpanded = () => {
  if (props.item.isDirectory) {
    emit('toggle', props.item);
  }
};

const getFileIconColor = () => {
  const extension = props.item.name.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'vue':
    case 'js':
    case 'ts':
      return 'rgba(76, 175, 80, 0.3)';
    case 'css':
    case 'scss':
    case 'less':
      return 'rgba(33, 150, 243, 0.3)';
    case 'html':
      return 'rgba(255, 87, 34, 0.3)';
    case 'json':
      return 'rgba(255, 193, 7, 0.3)';
    case 'md':
      return 'rgba(96, 125, 139, 0.3)';
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
      return 'rgba(156, 39, 176, 0.3)';
    default:
      return 'rgba(158, 158, 158, 0.3)';
  }
};

const getFileIconStroke = () => {
  const extension = props.item.name.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'vue':
    case 'js':
    case 'ts':
      return 'rgba(76, 175, 80, 0.8)';
    case 'css':
    case 'scss':
    case 'less':
      return 'rgba(33, 150, 243, 0.8)';
    case 'html':
      return 'rgba(255, 87, 34, 0.8)';
    case 'json':
      return 'rgba(255, 193, 7, 0.8)';
    case 'md':
      return 'rgba(96, 125, 139, 0.8)';
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
      return 'rgba(156, 39, 176, 0.8)';
    default:
      return 'rgba(158, 158, 158, 0.8)';
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
</script>

<style scoped>
.tree-node {
  user-select: none;
}

.node-content {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 24px;
  gap: 6px;
}

.node-content:hover {
  background: rgba(255, 255, 255, 0.1);
}

.node-content.selected {
  background: rgba(13, 255, 0, 0.2);
  border: 1px solid rgba(13, 255, 0, 0.4);
}

.expand-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.expand-icon:hover {
  background: rgba(255, 255, 255, 0.1);
}

.expand-placeholder {
  width: 16px;
  height: 16px;
}

.file-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-name {
  flex: 1;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.file-size {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.children {
  margin-left: 0;
}

/* 动画效果 */
.children {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 1000px;
  }
}
</style>
