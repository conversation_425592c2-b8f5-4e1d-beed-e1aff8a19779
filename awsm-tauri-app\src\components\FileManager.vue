<template>
  <div class="file-manager" :class="{ 'expanded': isExpanded, 'collapsed': !isExpanded }">
    <!-- 主容器背景 -->
    <div class="manager-background"></div>

    <!-- 展开/收起按钮 -->
    <button class="expand-button" @click="toggleExpanded" :title="isExpanded ? '收起' : '展开'">
      <svg width="12" height="21" viewBox="0 0 12 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path 
          :d="isExpanded ? 'M8 5L4 10L8 15' : 'M4 5L8 10L4 15'" 
          stroke="#8A8A8A" 
          stroke-width="2" 
          stroke-linecap="round" 
          stroke-linejoin="round"
        />
      </svg>
    </button>

    <!-- 文件管理器内容 -->
    <div class="manager-content" v-show="isExpanded">
      <!-- 标题栏 -->
      <div class="manager-header">
        <h3 class="manager-title">文件管理器</h3>
        <div class="header-actions">
          <button class="action-btn" @click="refreshFiles" title="刷新">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C9.61386 21 7.5008 19.8525 6.09173 18.1332" stroke="rgba(255,255,255,0.7)" stroke-width="2" stroke-linecap="round"/>
              <path d="M3 12L6 9M3 12L6 15" stroke="rgba(255,255,255,0.7)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
          <button class="action-btn" @click="openCurrentDirectory" title="打开当前目录">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 6H5C3.89543 6 3 6.89543 3 8V18C3 19.1046 3.89543 20 5 20H19C20.1046 20 21 19.1046 21 18V10C21 8.89543 20.1046 8 19 8H12L10 6Z" stroke="rgba(255,255,255,0.7)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 当前路径 -->
      <div class="current-path">
        <span class="path-text">{{ currentPath || '未选择目录' }}</span>
      </div>

      <!-- 文件树 -->
      <div class="file-tree" @scroll="handleScroll">
        <div v-if="loading" class="loading-indicator">
          <div class="loading-spinner"></div>
          <span>加载中...</span>
        </div>
        
        <div v-else-if="fileTree.length === 0" class="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 6H5C3.89543 6 3 6.89543 3 8V18C3 19.1046 3.89543 20 5 20H19C20.1046 20 21 19.1046 21 18V10C21 8.89543 20.1046 8 19 8H12L10 6Z" stroke="rgba(255,255,255,0.3)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <p>暂无文件</p>
          <button class="select-folder-btn" @click="selectFolder">选择文件夹</button>
        </div>

        <div v-else class="tree-content">
          <FileTreeNode
            v-for="item in fileTree"
            :key="item.path"
            :item="item"
            :level="0"
            :selected-path="selectedFile?.path"
            @select="handleFileSelect"
            @toggle="handleFolderToggle"
          />
        </div>
      </div>
    </div>

    <!-- 自定义滚动条 -->
    <div class="custom-scrollbar" v-show="isExpanded && showScrollbar">
      <div class="scrollbar-track" @click="handleTrackClick" ref="scrollbarTrack">
        <div
          class="scrollbar-thumb"
          :style="{
            height: thumbHeight + 'px',
            top: thumbTop + 'px'
          }"
          @mousedown="startDrag"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { getCurrentWindow } from '@tauri-apps/api/window';
import FileTreeNode from './FileTreeNode.vue';

// 接口定义
interface FileItem {
  name: string;
  path: string;
  isDirectory: boolean;
  children?: FileItem[];
  isExpanded?: boolean;
  size?: number;
  modified?: Date;
}

// 响应式数据
const isExpanded = ref(false);
const loading = ref(false);
const currentPath = ref('');
const fileTree = ref<FileItem[]>([]);
const selectedFile = ref<FileItem | null>(null);

// 滚动条相关
const showScrollbar = ref(false);
const thumbHeight = ref(50);
const thumbTop = ref(0);
const isDragging = ref(false);
const dragStartY = ref(0);
const dragStartScrollTop = ref(0);
const scrollbarTrack = ref<HTMLElement | null>(null);

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
  if (isExpanded.value && fileTree.value.length === 0) {
    loadDefaultDirectory();
  }
  nextTick(() => {
    updateScrollbar();
  });
};

const selectFolder = async () => {
  try {
    // 暂时使用模拟数据，实际项目中可以启用Tauri的dialog API
    console.log('选择文件夹功能 - 使用模拟数据');
    currentPath.value = 'E:\\Projects\\MyProject';
    await loadDefaultDirectory();

    // 实际实现时可以取消注释以下代码：
    // const { open } = await import('@tauri-apps/api/dialog');
    // const selected = await open({
    //   directory: true,
    //   multiple: false,
    // });
    // if (selected) {
    //   currentPath.value = selected as string;
    //   await loadDirectory(selected as string);
    // }
  } catch (error) {
    console.error('选择文件夹失败:', error);
  }
};

const loadDefaultDirectory = async () => {
  loading.value = true;
  try {
    // 模拟加载默认目录
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟文件树数据
    fileTree.value = [
      {
        name: '项目文档',
        path: '/项目文档',
        isDirectory: true,
        isExpanded: false,
        children: [
          { name: '需求文档.md', path: '/项目文档/需求文档.md', isDirectory: false, size: 1024 },
          { name: '设计文档.md', path: '/项目文档/设计文档.md', isDirectory: false, size: 2048 },
          { name: 'API文档.html', path: '/项目文档/API文档.html', isDirectory: false, size: 4096 },
        ]
      },
      {
        name: '源代码',
        path: '/源代码',
        isDirectory: true,
        isExpanded: false,
        children: [
          { name: 'main.ts', path: '/源代码/main.ts', isDirectory: false, size: 512 },
          { name: 'App.vue', path: '/源代码/App.vue', isDirectory: false, size: 1536 },
          { name: 'style.css', path: '/源代码/style.css', isDirectory: false, size: 768 },
          {
            name: 'components',
            path: '/源代码/components',
            isDirectory: true,
            isExpanded: false,
            children: [
              { name: 'FileManager.vue', path: '/源代码/components/FileManager.vue', isDirectory: false, size: 8192 },
              { name: 'FileTreeNode.vue', path: '/源代码/components/FileTreeNode.vue', isDirectory: false, size: 3072 },
            ]
          }
        ]
      },
      {
        name: '资源文件',
        path: '/资源文件',
        isDirectory: true,
        isExpanded: false,
        children: [
          { name: 'logo.png', path: '/资源文件/logo.png', isDirectory: false, size: 15360 },
          { name: 'icon.svg', path: '/资源文件/icon.svg', isDirectory: false, size: 2048 },
          { name: 'background.jpg', path: '/资源文件/background.jpg', isDirectory: false, size: 102400 },
        ]
      },
      { name: 'README.md', path: '/README.md', isDirectory: false, size: 256 },
      { name: 'package.json', path: '/package.json', isDirectory: false, size: 512 },
      { name: 'tsconfig.json', path: '/tsconfig.json', isDirectory: false, size: 384 },
    ];
    
    currentPath.value = '当前工作目录';
  } catch (error) {
    console.error('加载目录失败:', error);
  } finally {
    loading.value = false;
    nextTick(() => {
      updateScrollbar();
    });
  }
};

const refreshFiles = async () => {
  if (currentPath.value) {
    await loadDefaultDirectory();
  }
};

const openCurrentDirectory = async () => {
  try {
    // 模拟打开当前目录
    console.log('打开当前目录:', currentPath.value);
    // 实际实现时可以使用Tauri的shell API
    // const { open } = await import('@tauri-apps/api/shell');
    // await open(currentPath.value);
  } catch (error) {
    console.error('打开目录失败:', error);
  }
};

const handleFileSelect = (file: FileItem) => {
  selectedFile.value = file;
  console.log('选中文件:', file);
  // 触发文件选择事件
  emit('fileSelect', file);
};

const handleFolderToggle = async (folder: FileItem) => {
  folder.isExpanded = !folder.isExpanded;
  if (folder.isExpanded && (!folder.children || folder.children.length === 0)) {
    // TODO: 加载子目录内容
    console.log('加载子目录:', folder.path);
  }
  nextTick(() => {
    updateScrollbar();
  });
};

// 滚动条相关方法
const getContentElement = (): HTMLElement | null => {
  return document.querySelector('.file-tree');
};

const updateScrollbar = () => {
  nextTick(() => {
    const contentEl = getContentElement();
    if (!contentEl) return;

    const { scrollTop, scrollHeight, clientHeight } = contentEl;
    showScrollbar.value = scrollHeight > clientHeight;

    if (showScrollbar.value && scrollbarTrack.value) {
      const trackHeight = scrollbarTrack.value.clientHeight;
      const thumbHeightRatio = clientHeight / scrollHeight;
      thumbHeight.value = Math.max(trackHeight * thumbHeightRatio, 20);
      thumbHeight.value = Math.min(thumbHeight.value, trackHeight);

      const maxScrollTop = scrollHeight - clientHeight;
      const scrollRatio = maxScrollTop > 0 ? scrollTop / maxScrollTop : 0;
      const maxThumbTop = trackHeight - thumbHeight.value;
      thumbTop.value = Math.max(0, Math.min(scrollRatio * maxThumbTop, maxThumbTop));
    }
  });
};

const handleScroll = () => {
  if (!isDragging.value) {
    updateScrollbar();
  }
};

const handleTrackClick = (event: MouseEvent) => {
  const contentEl = getContentElement();
  if (!contentEl || !scrollbarTrack.value) return;

  const trackRect = scrollbarTrack.value.getBoundingClientRect();
  const clickY = event.clientY - trackRect.top;
  const trackHeight = scrollbarTrack.value.clientHeight;
  const clickRatio = clickY / trackHeight;
  const maxScrollTop = contentEl.scrollHeight - contentEl.clientHeight;

  contentEl.scrollTop = clickRatio * maxScrollTop;
  updateScrollbar();
};

const startDrag = (event: MouseEvent) => {
  const contentEl = getContentElement();
  if (!contentEl) return;

  isDragging.value = true;
  dragStartY.value = event.clientY;
  dragStartScrollTop.value = contentEl.scrollTop;

  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
  event.preventDefault();
};

const handleDrag = (event: MouseEvent) => {
  if (!isDragging.value || !scrollbarTrack.value) return;

  const contentEl = getContentElement();
  if (!contentEl) return;

  const deltaY = event.clientY - dragStartY.value;
  const trackHeight = scrollbarTrack.value.clientHeight;
  const maxScrollTop = contentEl.scrollHeight - contentEl.clientHeight;
  const dragRatio = deltaY / trackHeight;
  const newScrollTop = dragStartScrollTop.value + (dragRatio * maxScrollTop);

  contentEl.scrollTop = Math.max(0, Math.min(newScrollTop, maxScrollTop));
  updateScrollbar();
};

const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 事件定义
const emit = defineEmits<{
  fileSelect: [file: FileItem]
}>();

// 生命周期
onMounted(() => {
  updateScrollbar();
});

onUnmounted(() => {
  stopDrag();
});
</script>

<style scoped>
.file-manager {
  position: absolute;
  transition: all 0.3s ease-in-out;
  z-index: 100;
}

/* 收起状态 - 只显示展开按钮 */
.file-manager.collapsed {
  top: 205px;
  left: 0;
  width: 24px;
  height: 21px;
}

/* 展开状态 - 显示完整文件管理器 */
.file-manager.expanded {
  top: 58px;
  left: 0;
  width: 264px;
  height: 431px;
}

/* 主背景容器 - 匹配Figma设计 */
.manager-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.07) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: 0px 15px 15px 0px;
  backdrop-filter: blur(80px);
  box-sizing: border-box;
}

/* 展开/收起按钮 */
.expand-button {
  position: absolute;
  top: 50%;
  right: -12px;
  transform: translateY(-50%);
  width: 12px;
  height: 21px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 101;
}

.file-manager.collapsed .expand-button {
  right: -12px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
}

.file-manager.expanded .expand-button {
  right: -12px;
  background: rgba(255, 255, 255, 0.1);
}

.expand-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-50%) scale(1.05);
}

.expand-button svg {
  transition: transform 0.2s ease;
}

/* 文件管理器内容 */
.manager-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
}

/* 标题栏 */
.manager-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.manager-title {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #FFFFFF;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 6px;
}

.action-btn {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(13, 255, 0, 0.5);
}

/* 当前路径 */
.current-path {
  margin-bottom: 12px;
  padding: 6px 8px;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.path-text {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 文件树容器 */
.file-tree {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  margin-right: 8px;
}

.file-tree::-webkit-scrollbar {
  display: none;
}

.file-tree {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* 加载状态 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
  gap: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top: 2px solid rgba(13, 255, 0, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.empty-state p {
  margin: 0;
  font-size: 12px;
}

.select-folder-btn {
  padding: 8px 16px;
  background: rgba(13, 255, 0, 0.1);
  border: 1px solid rgba(13, 255, 0, 0.3);
  border-radius: 6px;
  color: rgba(13, 255, 0, 0.9);
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-folder-btn:hover {
  background: rgba(13, 255, 0, 0.2);
  border-color: rgba(13, 255, 0, 0.5);
}

/* 文件树内容 */
.tree-content {
  padding: 4px 0;
}

/* 自定义滚动条 */
.custom-scrollbar {
  position: absolute;
  top: 80px;
  right: 4px;
  width: 6px;
  bottom: 16px;
  z-index: 10;
}

.scrollbar-track {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(138, 138, 138, 0.2);
  border: 1px solid rgba(138, 138, 138, 0.1);
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.scrollbar-track:hover {
  background: rgba(138, 138, 138, 0.3);
}

.scrollbar-thumb {
  position: absolute;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 3px;
  cursor: grab;
  transition: background-color 0.2s ease;
  min-height: 16px;
}

.scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.7);
}

.scrollbar-thumb:active {
  cursor: grabbing;
  background: rgba(13, 255, 0, 0.6);
}
</style>
