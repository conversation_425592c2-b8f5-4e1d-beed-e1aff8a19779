<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { getCurrentWindow, LogicalSize, LogicalPosition } from '@tauri-apps/api/window';
import ParameterPanel from './components/ParameterPanel.vue';
import CreationStatusPanel from './components/CreationStatusPanel.vue';
import WorkPanel from './components/WorkPanel.vue';
import FileManager from './components/FileManager.vue';

// 选中框位置状态
const selectedIconPosition = ref({
  top: 112, // 默认主页图标位置，调整为中心对齐 (向上移动3px)
  left: 8.5,  // 默认主页图标位置，调整为中心对齐
  width: 40,  // 默认选中框宽度
  height: 40  // 默认选中框高度
});

// 图标点击反馈状态
const iconClickFeedback = ref({
  about: false,
  settings: false,
  logout: false,
  home: false,
  work: false,
  bookshelf: false,
  statistics: false,
  my: false
});

// 窗口全屏状态
const isFullscreen = ref(false);
// 防抖标志
const isTogglingFullscreen = ref(false);
// 保存窗口原始状态
const windowState = ref({
  x: 0,
  y: 0,
  width: 800,
  height: 600
});

// 切换最大化模式（使用窗口最大化替代全屏）
const maximizeWindow = async () => {
  // 防抖处理，避免频繁点击
  if (isTogglingFullscreen.value) {
    return;
  }
  
  try {
    isTogglingFullscreen.value = true;
    
    // 获取当前窗口
    const appWindow = getCurrentWindow();
    
    // 切换最大化模式
    const isCurrentlyMaximized = await appWindow.isMaximized();
    if (isCurrentlyMaximized) {
      // 取消最大化时，恢复到保存的窗口状态，但确保不小于最小尺寸
      const restoreWidth = Math.max(windowState.value.width, 1300);
      const restoreHeight = Math.max(windowState.value.height, 840);
      await appWindow.setSize(new LogicalSize(restoreWidth, restoreHeight));
      await appWindow.setPosition(new LogicalPosition(windowState.value.x, windowState.value.y));
    } else {
      // 最大化前，保存当前窗口状态
      const currentSize = await appWindow.innerSize();
      const currentPosition = await appWindow.innerPosition();
      windowState.value = {
        x: currentPosition.x,
        y: currentPosition.y,
        width: currentSize.width,
        height: currentSize.height
      };
      await appWindow.maximize();
    }
    
    // 更新状态
    isFullscreen.value = !isCurrentlyMaximized;
    
  } catch (error) {
    console.error('Toggle maximize error:', error);
  } finally {
    // 延迟释放防抖标志
    setTimeout(() => {
      isTogglingFullscreen.value = false;
    }, 100);
  }
};

// 更新选中框位置的方法
const updateSelectionBoxPosition = (top: number, left: number, width: number = 40, height: number = 40) => {
  selectedIconPosition.value = { top, left, width, height };
};

// 图标点击反馈方法
const triggerIconFeedback = (iconName: 'about' | 'settings' | 'logout' | 'home' | 'work' | 'bookshelf' | 'statistics' | 'my') => {
  // 设置点击状态为true
  iconClickFeedback.value[iconName] = true;
  
  // 300ms后恢复原状
  setTimeout(() => {
    iconClickFeedback.value[iconName] = false;
  }, 300);
};

// 主页图标点击处理方法
const handleHomeIconClick = () => {
  // 触发主页图标点击反馈效果
  triggerIconFeedback('home');
  // 更新选中框位置到主页图标
  updateSelectionBoxPosition(115, 7.5);
};

// 工作图标点击处理方法
const handleWorkIconClick = () => {
  // 触发工作图标点击反馈效果
  triggerIconFeedback('work');
  // 更新选中框位置到工作图标
  updateSelectionBoxPosition(176, 7, 29, 29);
};

// 书架图标点击处理方法
const handleBookshelfIconClick = () => {
  // 触发书架图标点击反馈效果
  triggerIconFeedback('bookshelf');
  // 更新选中框位置到书架图标
  updateSelectionBoxPosition(242, 6.5, 28, 28);
};

// 统计图标点击处理方法
const handleStatisticsIconClick = () => {
  // 触发统计图标点击反馈效果
  triggerIconFeedback('statistics');
  // 更新选中框位置到统计图标
  updateSelectionBoxPosition(310, 7, 33, 33);
};

// 我的图标点击处理方法
const handleMyIconClick = () => {
  // 触发我的图标点击反馈效果
  triggerIconFeedback('my');
  // 更新选中框位置到我的图标
  updateSelectionBoxPosition(376, 7, 35, 34);
};

// 最小化窗口
const minimizeWindow = async () => {
  try {
    // 调用Rust命令来最小化窗口
    const { invoke } = await import('@tauri-apps/api/core');
    await invoke('minimize_window');
  } catch (error) {
    console.error('Minimize window error:', error);
  }
};

// 关闭窗口
const closeWindow = async () => {
  try {
    // 调用Rust命令来关闭窗口
    const { invoke } = await import('@tauri-apps/api/core');
    console.log('Attempting to close window...');
    await invoke('close_window');
    console.log('Window closed successfully.');
  } catch (error) {
    console.error('Close window error:', error);
    // 如果在非Tauri环境中，尝试使用window.close()
    if (typeof (window as any).__TAURI__ === 'undefined') {
      console.log('Not in Tauri environment, trying window.close()...');
      window.close();
    }
  }
};

// 使用节流优化性能
let ticking = false;

// 轻量级窗口大小变化处理
const handleResize = () => {
  if (!ticking) {
    requestAnimationFrame(() => {
      // 仅在必要时更新关键元素
      const sidebar = document.querySelector('.sidebar') as HTMLElement;
      if (sidebar) {
        // 使用硬件加速优化
        sidebar.style.transform = `translateZ(0)`;
      }
      ticking = false;
    });
    ticking = true;
  }
};

onMounted(() => {
  // 使用节流优化的 resize 事件监听器
  let resizeTimeout: number | null = null;
  const optimizedResize = () => {
    if (resizeTimeout) {
      clearTimeout(resizeTimeout);
    }
    resizeTimeout = window.setTimeout(handleResize, 10); // 10ms 节流
  };
  
  window.addEventListener('resize', optimizedResize);
  
  // 清理函数
  onUnmounted(() => {
    if (resizeTimeout) {
      clearTimeout(resizeTimeout);
    }
    window.removeEventListener('resize', optimizedResize);
  });
});

// 文件选择处理
const handleFileSelect = (file: any) => {
  console.log('选中文件:', file);
  // 这里可以添加文件选择后的处理逻辑
  // 比如在WorkPanel中打开文件等
};
</script>

<template>
  <div class="background">
    <div class="sidebar">
      <div class="app-logo-container">
        <img src="/src-tauri/icons/Vector.png" alt="App Logo" width="42" height="42" />
      </div>
      <!-- 主页图标和选中框容器 -->
      <div class="icon-selection-container">
        <!-- 主页图标和选中框容器 -->
        <div class="icon-container">
          <!-- 主页图标 -->
          <div class="home-icon-container" @click="handleHomeIconClick">
            <svg class="home-icon" width="35" height="35" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18.6225 22.7041H6.37756C5.38266 22.7041 4.56633 21.8877 4.56633 20.8801V13.8265C4.56633 13.6607 4.43878 13.5331 4.28572 13.5331H3.6097C3.07398 13.5331 2.6148 13.227 2.41072 12.7296C2.20664 12.2449 2.32143 11.6837 2.69133 11.301L11.0204 2.90814C11.4031 2.51274 11.9388 2.2959 12.5 2.2959C13.0612 2.2959 13.5969 2.51274 13.9924 2.9209L22.3214 11.3138C22.6913 11.6964 22.8061 12.2576 22.602 12.7423C22.398 13.2398 21.9388 13.5459 21.4031 13.5459H20.727C20.574 13.5459 20.4464 13.6734 20.4464 13.8393V20.8928C20.4337 21.8877 19.6174 22.7041 18.6225 22.7041ZM12.5 3.31631C12.2066 3.31631 11.9388 3.4311 11.7347 3.63518L3.40562 12.028C3.27807 12.1556 3.31633 12.2959 3.34184 12.3469C3.36735 12.3979 3.43113 12.5255 3.6097 12.5255H4.28572C5 12.5255 5.58674 13.1122 5.58674 13.8393V20.8928C5.58674 21.3265 5.94388 21.6964 6.37756 21.6964H18.6225C19.0561 21.6964 19.4133 21.3393 19.4133 20.8928V13.8393C19.4133 13.1122 20 12.5255 20.7143 12.5255H21.3903C21.5561 12.5255 21.6327 12.3979 21.6582 12.3469C21.6837 12.2959 21.7219 12.1556 21.5944 12.028L13.2653 3.63518C13.0612 3.4311 12.7934 3.31631 12.5 3.31631Z" fill="#FFFFFF"/>
              <path d="M16.199 22.7041H8.80103V17.347C8.80103 16.2245 9.71939 15.3062 10.8418 15.3062H14.1582C15.2806 15.3062 16.199 16.2245 16.199 17.347V22.7041ZM9.82143 21.6837H15.1786V17.347C15.1786 16.7857 14.7194 16.3266 14.1582 16.3266H10.8418C10.2806 16.3266 9.82143 16.7857 9.82143 17.347V21.6837Z" fill="#FFFFFF"/>
            </svg>
            <!-- 主页图标名称 -->
            <div class="icon-name-tooltip">
              <svg width="60" height="18" viewBox="0 0 60 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-name-bg">
                <path d="M0.202424 9.30693C-0.0674747 9.12871 -0.0674747 8.83168 0.202424 8.69307L2.9314 7.10891C3.2013 6.93069 3.44121 6.77228 3.44121 6.51485V1.80198C3.44121 0.811881 4.67075 0 6.17019 0H57.271C58.7705 0 60 0.811881 60 1.80198V16.198C60 17.1881 58.7705 18 57.271 18H6.1402C4.64076 18 3.41122 17.1881 3.41122 16.198V11.4653C3.41122 11.2079 3.17131 11.0495 2.90141 10.8713L0.202424 9.30693Z" fill="#2C2C2C"/>
                <path d="M6.16992 0.0498047H57.2715C58.0124 0.0498883 58.6844 0.250804 59.1699 0.571289C59.656 0.892231 59.9501 1.3291 59.9502 1.80176V16.1982C59.9501 16.6709 59.656 17.1078 59.1699 17.4287C58.6844 17.7492 58.0124 17.9501 57.2715 17.9502H6.14062C5.39953 17.9502 4.72682 17.7493 4.24121 17.4287C3.75517 17.1078 3.46103 16.6709 3.46094 16.1982V11.4658C3.46094 11.3194 3.39218 11.2031 3.29297 11.1035C3.19492 11.0051 3.06243 10.9174 2.92871 10.8291L2.92676 10.8281L0.227539 9.26367C0.104663 9.18167 0.0498845 9.07775 0.0498047 8.98535C0.0498047 8.89357 0.104125 8.79968 0.225586 8.7373L0.227539 8.73633L2.95605 7.15234H2.95703L2.95898 7.15039C3.09266 7.06212 3.22522 6.97533 3.32324 6.87695C3.42245 6.77735 3.49121 6.66108 3.49121 6.51465V1.80176C3.4913 1.32918 3.78461 0.892215 4.27051 0.571289C4.75607 0.250663 5.42881 0.0498525 6.16992 0.0498047Z" stroke="#484848" stroke-opacity="0.8" stroke-width="0.1"/>
              </svg>
              <span class="icon-name-text">主页</span>
            </div>
          </div>
          <!-- 选中框 -->
          <div class="selection-box-container" :style="{ top: selectedIconPosition.top + 'px', left: selectedIconPosition.left + 'px' }">
            <svg class="selection-box" width="45" height="45" viewBox="0 0 35 35" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect width="35" height="35" rx="10" fill="white" fill-opacity="0.1"/>
              <rect x="0.5" y="0.5" width="34" height="34" rx="9.5" stroke="url(#paint0_linear_16_49)" stroke-opacity="0.3"/>
              <defs>
                <linearGradient id="paint0_linear_16_49" x1="17.5" y1="0" x2="17.5" y2="35" gradientUnits="userSpaceOnUse">
                  <stop stop-color="white" stop-opacity="0.95"/>
                  <stop offset="1" stop-color="white" stop-opacity="0"/>
                </linearGradient>
              </defs>
            </svg>
          </div>
        </div>
      </div>
      <!-- 工作图标 -->
      <div class="work-icon-container" @click="handleWorkIconClick">
        <svg class="work-icon" width="34" height="34" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M16.325 23.875H5.24997C3.09998 23.875 1.34998 22.125 1.34998 19.975V4.525C1.34998 2.375 3.09998 0.625 5.24997 0.625H16.325C18.475 0.625 20.225 2.375 20.225 4.525V19.975C20.225 22.125 18.475 23.875 16.325 23.875ZM5.24997 1.925C3.82498 1.925 2.64998 3.1 2.64998 4.525V19.975C2.64998 21.4 3.82498 22.575 5.24997 22.575H16.325C17.75 22.575 18.925 21.4 18.925 19.975V4.525C18.925 3.1 17.75 1.925 16.325 1.925H5.24997Z" fill="#FFFFFF"/>
          <path d="M5.5 1H6.8V23.5H5.5V1Z" fill="#FFFFFF"/>
          <path d="M2.5 6.09998H5.925V7.39998H2.5V6.09998ZM2.5 11.6H5.925V12.9H2.5V11.6ZM2.5 17.075H5.925V18.375H2.5V17.075Z" fill="#FFFFFF"/>
          <path d="M15.375 8.87495H11.075C10.1 8.87495 9.29999 8.07495 9.29999 7.09995V5.84995C9.29999 4.87495 10.1 4.07495 11.075 4.07495H15.375C16.35 4.07495 17.15 4.87495 17.15 5.84995V7.09995C17.125 8.09995 16.35 8.87495 15.375 8.87495ZM11.075 5.57495C10.925 5.57495 10.8 5.69995 10.8 5.84995V7.09995C10.8 7.24995 10.925 7.37495 11.075 7.37495H15.375C15.525 7.37495 15.65 7.24995 15.65 7.09995V5.84995C15.65 5.69995 15.525 5.57495 15.375 5.57495H11.075Z" fill="#FFFFFF"/>
        </svg>
        <!-- 工作图标名称 -->
        <div class="icon-name-tooltip">
          <svg width="60" height="18" viewBox="0 0 60 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-name-bg">
            <path d="M0.202424 9.30693C-0.0674747 9.12871 -0.0674747 8.83168 0.202424 8.69307L2.9314 7.10891C3.2013 6.93069 3.44121 6.77228 3.44121 6.51485V1.80198C3.44121 0.811881 4.67075 0 6.17019 0H57.271C58.7705 0 60 0.811881 60 1.80198V16.198C60 17.1881 58.7705 18 57.271 18H6.1402C4.64076 18 3.41122 17.1881 3.41122 16.198V11.4653C3.41122 11.2079 3.17131 11.0495 2.90141 10.8713L0.202424 9.30693Z" fill="#2C2C2C"/>
            <path d="M6.16992 0.0498047H57.2715C58.0124 0.0498883 58.6844 0.250804 59.1699 0.571289C59.656 0.892231 59.9501 1.3291 59.9502 1.80176V16.1982C59.9501 16.6709 59.656 17.1078 59.1699 17.4287C58.6844 17.7492 58.0124 17.9501 57.2715 17.9502H6.14062C5.39953 17.9502 4.72682 17.7493 4.24121 17.4287C3.75517 17.1078 3.46103 16.6709 3.46094 16.1982V11.4658C3.46094 11.3194 3.39218 11.2031 3.29297 11.1035C3.19492 11.0051 3.06243 10.9174 2.92871 10.8291L2.92676 10.8281L0.227539 9.26367C0.104663 9.18167 0.0498845 9.07775 0.0498047 8.98535C0.0498047 8.89357 0.104125 8.79968 0.225586 8.7373L0.227539 8.73633L2.95605 7.15234H2.95703L2.95898 7.15039C3.09266 7.06212 3.22522 6.97533 3.32324 6.87695C3.42245 6.77735 3.49121 6.66108 3.49121 6.51465V1.80176C3.4913 1.32918 3.78461 0.892215 4.27051 0.571289C4.75607 0.250663 5.42881 0.0498525 6.16992 0.0498047Z" stroke="#484848" stroke-opacity="0.8" stroke-width="0.1"/>
          </svg>
          <span class="icon-name-text">工作</span>
        </div>
      </div>
      <!-- 书架图标 -->
      <div class="bookshelf-icon-container" @click="handleBookshelfIconClick">
        <svg class="bookshelf-icon" width="33" height="33" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M20.9366 18.6316H2.65948C2.42865 18.6316 2.20727 18.7233 2.04405 18.8865C1.88082 19.0497 1.78912 19.2711 1.78912 19.5019V20.8075C1.78912 21.0383 1.88082 21.2597 2.04405 21.4229C2.20727 21.5861 1.78912 21.6778 2.65948 21.6778H20.9366C21.1674 21.6778 21.3888 21.5861 21.552 21.4229C21.7152 21.2597 21.8069 21.0383 21.8069 20.8075V19.5019C21.8069 19.2711 21.7152 19.0497 21.552 18.8865C21.3888 18.7233 21.1674 18.6316 20.9366 18.6316ZM20.3224 20.3721H3.27321C3.22574 20.3721 3.18021 20.3532 3.14664 20.3196C3.11307 20.2861 3.09422 20.2405 3.09422 20.1931V20.1159C3.09422 20.0924 3.09885 20.0691 3.10784 20.0474C3.11684 20.0257 3.13002 20.0059 3.14664 19.9893C3.16326 19.9727 3.18299 19.9595 3.20471 19.9505C3.22643 19.9415 3.2497 19.9369 3.27321 19.9369H20.3224C20.3459 19.9369 20.3692 19.9415 20.3909 19.9505C20.4126 19.9595 20.4324 19.9727 20.449 19.9893C20.4656 20.0059 20.4788 20.0257 20.4878 20.0474C20.4968 20.0691 20.5014 20.0924 20.5014 20.1159V20.1931C20.5014 20.2405 20.4825 20.2861 20.449 20.3196C20.4154 20.3532 20.3699 20.3721 20.3224 20.3721ZM2.65948 17.7612H5.27056C5.50139 17.7612 5.72277 17.6695 5.886 17.5063C6.04922 17.3431 6.14092 17.1217 6.14092 16.8909V5.57641C6.14092 5.34557 6.04922 5.12419 5.886 4.96097C5.72277 4.79775 5.50139 4.70605 5.27056 4.70605H2.65948C2.42865 4.70605 2.20727 4.79775 2.04405 4.96097C1.88082 5.12419 1.78912 5.34557 1.78912 5.57641V16.8909C1.78912 17.1217 1.88082 17.3431 2.04405 17.5063C2.20727 17.6695 2.42865 17.7612 2.65948 17.7612ZM3.28596 5.93328H4.64632C4.71753 5.93328 4.78582 5.96156 4.83617 6.01192C4.88652 6.06227 4.91481 6.13056 4.91481 6.20177V16.2655C4.91481 16.3008 4.88088 16.3357 4.83617 16.3682C4.78164 16.4008 4.72104 16.4304 4.64632 16.534H3.28596C3.2507 16.534 3.21579 16.527 3.18322 16.5135C3.15064 16.5001 3.12104 16.4803 3.09611 16.4553C3.07118 16.4304 3.0514 16.4008 3.03791 16.3682C3.02442 16.3357 3.01747 16.3008 3.01747 16.2655V6.20177C3.01747 6.16651 3.02442 6.1316 3.03791 6.09902C3.0514 6.06645 3.07118 6.03685 3.09611 6.01192C3.12104 5.98698 3.15064 5.96721 3.18322 5.95371C3.21579 5.94022 3.2507 5.93328 3.28596 5.93328ZM8.3166 17.7612H10.9277C11.1585 17.7612 11.3799 17.6695 11.5431 17.5063C11.7063 17.3431 11.798 17.1217 11.798 16.8909V6.60697L17.9129 17.3068L17.9185 17.3169C17.9753 17.4165 18.0512 17.504 18.1418 17.5744C18.2325 17.6447 18.3361 17.6965 18.4467 17.7267C18.5574 17.7569 18.6729 17.765 18.7867 17.7506C18.9005 17.7361 19.0104 17.6994 19.1099 17.6424L21.3729 16.3223C21.574 16.2011 21.7192 16.0056 21.7773 15.7781C21.8353 15.5506 21.8014 15.3094 21.683 15.1067L15.258 3.85762L15.2524 3.84755C15.1956 3.74787 15.1197 3.66036 15.0291 3.59004C14.9385 3.51972 14.8348 3.46796 14.7242 3.43772C14.6135 3.40748 14.498 3.39936 14.3842 3.41382C14.2704 3.42829 14.1605 3.46505 14.061 3.522L11.798 4.84208V2.53015C11.798 2.29932 11.7063 2.07794 11.5431 1.91471C11.3799 1.75149 11.1585 1.65979 10.9277 1.65979H8.3166C8.08576 1.65979 7.86438 1.75149 7.70116 1.91471C7.53793 2.07794 7.44624 2.29932 7.44624 2.53015V16.8909C7.44624 17.1217 7.53793 17.3431 7.70116 17.5063C7.86438 17.6695 8.08576 17.7612 8.3166 17.7612ZM14.5288 5.22468C14.5378 5.21948 14.5476 5.2161 14.5579 5.21474C14.5681 5.21338 14.5785 5.21406 14.5885 5.21674C14.5985 5.21942 14.6079 5.22406 14.616 5.23038C14.6242 5.23669 14.6311 5.24457 14.6362 5.25355L20.0841 14.785C20.1193 14.8464 20.1287 14.9193 20.1104 14.9877C20.0921 15.0561 20.0475 15.1145 19.9863 15.1501L18.8066 15.8384C18.776 15.8562 18.7422 15.8678 18.7072 15.8725C18.6721 15.8772 18.6365 15.875 18.6023 15.8659C18.5681 15.8568 18.5361 15.8411 18.508 15.8196C18.4799 15.7981 18.4564 15.7712 18.4387 15.7406L13.0854 6.37562C13.0503 6.31416 13.0409 6.24131 13.0592 6.17292C13.0775 6.10453 13.1221 6.04615 13.1832 6.01047L14.5288 5.22468ZM8.94196 2.88702H10.3023C10.3735 2.88702 10.4418 2.91531 10.4922 2.96566C10.5425 3.01601 10.5708 3.0843 10.5708 3.15551V16.2655C10.5708 16.3008 10.5639 16.3357 10.5504 16.3682C10.5369 16.4008 10.5171 16.4304 10.4922 16.4553C10.4672 16.4803 10.4376 16.5001 10.4051 16.5135C10.3725 16.527 10.3376 16.534 10.3023 16.534H8.94196C8.9067 16.534 8.87178 16.527 8.83921 16.5135C8.80663 16.5001 8.77704 16.4803 8.7521 16.4553C8.72717 16.4304 8.7074 16.4008 8.6939 16.3682C8.68041 16.3357 8.67346 16.3008 8.67346 16.2655V3.15551C8.67346 3.0843 8.70175 3.01601 8.7521 2.96566C8.80246 2.91531 8.87075 2.88702 8.94196 2.88702Z" fill="#FFFFFF"/>
        </svg>
        <!-- 书架图标名称 -->
        <div class="icon-name-tooltip">
          <svg width="60" height="18" viewBox="0 0 60 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-name-bg">
            <path d="M0.202424 9.30693C-0.0674747 9.12871 -0.0674747 8.83168 0.202424 8.69307L2.9314 7.10891C3.2013 6.93069 3.44121 6.77228 3.44121 6.51485V1.80198C3.44121 0.811881 4.67075 0 6.17019 0H57.271C58.7705 0 60 0.811881 60 1.80198V16.198C60 17.1881 58.7705 18 57.271 18H6.1402C4.64076 18 3.41122 17.1881 3.41122 16.198V11.4653C3.41122 11.2079 3.17131 11.0495 2.90141 10.8713L0.202424 9.30693Z" fill="#2C2C2C"/>
            <path d="M6.16992 0.0498047H57.2715C58.0124 0.0498883 58.6844 0.250804 59.1699 0.571289C59.656 0.892231 59.9501 1.3291 59.9502 1.80176V16.1982C59.9501 16.6709 59.656 17.1078 59.1699 17.4287C58.6844 17.7492 58.0124 17.9501 57.2715 17.9502H6.14062C5.39953 17.9502 4.72682 17.7493 4.24121 17.4287C3.75517 17.1078 3.46103 16.6709 3.46094 16.1982V11.4658C3.46094 11.3194 3.39218 11.2031 3.29297 11.1035C3.19492 11.0051 3.06243 10.9174 2.92871 10.8291L2.92676 10.8281L0.227539 9.26367C0.104663 9.18167 0.0498845 9.07775 0.0498047 8.98535C0.0498047 8.89357 0.104125 8.79968 0.225586 8.7373L0.227539 8.73633L2.95605 7.15234H2.95703L2.95898 7.15039C3.09266 7.06212 3.22522 6.97533 3.32324 6.87695C3.42245 6.77735 3.49121 6.66108 3.49121 6.51465V1.80176C3.4913 1.32918 3.78461 0.892215 4.27051 0.571289C4.75607 0.250663 5.42881 0.0498525 6.16992 0.0498047Z" stroke="#484848" stroke-opacity="0.8" stroke-width="0.1"/>
          </svg>
          <span class="icon-name-text">书架</span>
        </div>
      </div>
      <!-- 统计图标 -->
      <div class="statistics-icon-container" @click="handleStatisticsIconClick">
        <svg class="statistics-icon" width="38" height="38" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M22.7937 23.7125H5.26094C4.75508 23.7125 4.34766 23.3024 4.34766 22.7992V5.26643C4.34766 4.76057 4.75781 4.35315 5.26094 4.35315C5.76406 4.35315 6.17422 4.76331 6.17422 5.26643V21.8832H22.791C23.2969 21.8832 23.7043 22.2934 23.7043 22.7965C23.707 23.3024 23.2969 23.7125 22.7937 23.7125Z" fill="#FFFFFF"/>
          <path d="M7.69998 19.4442C7.46482 19.4442 7.2324 19.3539 7.05466 19.1762C6.69646 18.818 6.69646 18.241 7.05466 17.8828L11.5527 13.3848C11.9082 13.0293 12.4824 13.0293 12.8379 13.3766L15.5258 16.0043L20.2234 11.0387C20.5707 10.6723 21.1504 10.6559 21.5168 11.0031C21.8832 11.3504 21.8996 11.9301 21.5523 12.2965L16.2148 17.9375C16.0453 18.1152 15.8129 18.2192 15.5695 18.2246C15.3289 18.2246 15.0883 18.1344 14.9133 17.9649L12.2062 15.318L8.34802 19.1762C8.17029 19.3539 7.93513 19.4442 7.69998 19.4442Z" fill="#FFFFFF"/>
        </svg>
        <!-- 统计图标名称 -->
        <div class="icon-name-tooltip">
          <svg width="60" height="18" viewBox="0 0 60 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-name-bg">
            <path d="M0.202424 9.30693C-0.0674747 9.12871 -0.0674747 8.83168 0.202424 8.69307L2.9314 7.10891C3.2013 6.93069 3.44121 6.77228 3.44121 6.51485V1.80198C3.44121 0.811881 4.67075 0 6.17019 0H57.271C58.7705 0 60 0.811881 60 1.80198V16.198C60 17.1881 58.7705 18 57.271 18H6.1402C4.64076 18 3.41122 17.1881 3.41122 16.198V11.4653C3.41122 11.2079 3.17131 11.0495 2.90141 10.8713L0.202424 9.30693Z" fill="#2C2C2C"/>
            <path d="M6.16992 0.0498047H57.2715C58.0124 0.0498883 58.6844 0.250804 59.1699 0.571289C59.656 0.892231 59.9501 1.3291 59.9502 1.80176V16.1982C59.9501 16.6709 59.656 17.1078 59.1699 17.4287C58.6844 17.7492 58.0124 17.9501 57.2715 17.9502H6.14062C5.39953 17.9502 4.72682 17.7493 4.24121 17.4287C3.75517 17.1078 3.46103 16.6709 3.46094 16.1982V11.4658C3.46094 11.3194 3.39218 11.2031 3.29297 11.1035C3.19492 11.0051 3.06243 10.9174 2.92871 10.8291L2.92676 10.8281L0.227539 9.26367C0.104663 9.18167 0.0498845 9.07775 0.0498047 8.98535C0.0498047 8.89357 0.104125 8.79968 0.225586 8.7373L0.227539 8.73633L2.95605 7.15234H2.95703L2.95898 7.15039C3.09266 7.06212 3.22522 6.97533 3.32324 6.87695C3.42245 6.77735 3.49121 6.66108 3.49121 6.51465V1.80176C3.4913 1.32918 3.78461 0.892215 4.27051 0.571289C4.75607 0.250663 5.42881 0.0498525 6.16992 0.0498047Z" stroke="#484848" stroke-opacity="0.8" stroke-width="0.1"/>
          </svg>
          <span class="icon-name-text">统计</span>
        </div>
      </div>
      <!-- 我的图标 -->
      <div class="my-icon-container" @click="handleMyIconClick">
        <svg class="my-icon" width="40" height="39" viewBox="0 0 30 29" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M10.5292 22.2044C10.5292 22.2044 11.5152 23.4852 15 23.4852C18.4848 23.4852 19.4177 22.2044 19.4177 22.2044L17.9314 21.0154H11.5321L10.5292 22.2044Z" fill="#FFFFFF"/>
          <path d="M8.37836 13.9635C8.37836 14.833 8.54963 15.6941 8.8824 16.4975C9.21517 17.3008 9.70292 18.0308 10.3178 18.6457C10.9327 19.2606 11.6626 19.7483 12.466 20.0811C13.2694 20.4139 14.1305 20.5851 15 20.5851C15.8696 20.5851 16.7306 20.4139 17.534 20.0811C18.3374 19.7483 19.0674 19.2606 19.6822 18.6457C20.2971 18.0308 20.7849 17.3008 21.1176 16.4975C21.4504 15.6941 21.6217 14.833 21.6217 13.9635C21.6217 12.2073 20.9241 10.523 19.6822 9.28124C18.4404 8.03943 16.7562 7.3418 15 7.3418C13.2438 7.3418 11.5596 8.03943 10.3178 9.28124C9.07599 10.523 8.37836 12.2073 8.37836 13.9635Z" fill="#FFFFFF"/>
          <path d="M12.564 13.1466H12.3538C11.8221 13.1466 11.3871 12.7116 11.3871 12.18V10.8967C11.3871 10.3651 11.8221 9.93005 12.3538 9.93005H12.564C13.0957 9.93005 13.5307 10.3651 13.5307 10.8967V12.18C13.5307 12.7116 13.0957 13.1466 12.564 13.1466ZM17.6463 13.1466H17.436C16.9043 13.1466 16.4693 12.7116 16.4693 12.18V10.8967C16.4693 10.3651 16.9043 9.93005 17.436 9.93005H17.6463C18.1779 9.93005 18.6129 10.3651 18.6129 10.8967V12.18C18.6129 12.7116 18.1779 13.1466 17.6463 13.1466Z" fill="#333C4F"/>
          <path d="M20.3432 20.3677C22.9532 18.6325 24.6787 15.6648 24.6787 12.3033C24.6787 6.96726 20.336 2.62451 15 2.62451C9.66398 2.62451 5.32123 6.96726 5.32123 12.3033C5.32123 15.6648 7.04673 18.6325 9.65673 20.3677C9.3464 20.5364 9.05686 20.7408 8.79398 20.9767C7.68956 21.9772 7.13131 23.4683 7.13131 25.4064C7.13131 25.9405 7.56389 26.3731 8.09798 26.3731C8.63206 26.3731 9.06464 25.9405 9.06464 25.4064C9.06464 24.0434 9.40539 23.0405 10.0796 22.4194C11.0076 21.5663 12.332 21.6582 12.3682 21.663C12.4262 21.6678 12.4842 21.6678 12.5398 21.663C14.1503 22.0853 15.8424 22.0853 17.4529 21.663C17.5133 21.6678 17.5737 21.6703 17.6341 21.663C17.6486 21.663 18.9681 21.5591 19.9058 22.4073C20.5873 23.026 20.9329 24.0338 20.9329 25.4064C20.9329 25.9405 21.3655 26.3731 21.8996 26.3731C22.4336 26.3731 22.8662 25.9405 22.8662 25.4064C22.8662 23.4658 22.308 21.9772 21.2036 20.9767C20.942 20.7403 20.6531 20.5358 20.3432 20.3677ZM7.25456 12.3033C7.25456 8.03301 10.7297 4.55784 15 4.55784C19.2702 4.55784 22.7454 8.03301 22.7454 12.3033C22.7454 16.5735 19.2702 20.0487 15 20.0487C10.7297 20.0487 7.25456 16.5735 7.25456 12.3033Z" fill="#363434"/>
        </svg>
        <!-- 我的图标名称 -->
        <div class="icon-name-tooltip">
          <svg width="60" height="18" viewBox="0 0 60 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-name-bg">
            <path d="M0.202424 9.30693C-0.0674747 9.12871 -0.0674747 8.83168 0.202424 8.69307L2.9314 7.10891C3.2013 6.93069 3.44121 6.77228 3.44121 6.51485V1.80198C3.44121 0.811881 4.67075 0 6.17019 0H57.271C58.7705 0 60 0.811881 60 1.80198V16.198C60 17.1881 58.7705 18 57.271 18H6.1402C4.64076 18 3.41122 17.1881 3.41122 16.198V11.4653C3.41122 11.2079 3.17131 11.0495 2.90141 10.8713L0.202424 9.30693Z" fill="#2C2C2C"/>
            <path d="M6.16992 0.0498047H57.2715C58.0124 0.0498883 58.6844 0.250804 59.1699 0.571289C59.656 0.892231 59.9501 1.3291 59.9502 1.80176V16.1982C59.9501 16.6709 59.656 17.1078 59.1699 17.4287C58.6844 17.7492 58.0124 17.9501 57.2715 17.9502H6.14062C5.39953 17.9502 4.72682 17.7493 4.24121 17.4287C3.75517 17.1078 3.46103 16.6709 3.46094 16.1982V11.4658C3.46094 11.3194 3.39218 11.2031 3.29297 11.1035C3.19492 11.0051 3.06243 10.9174 2.92871 10.8291L2.92676 10.8281L0.227539 9.26367C0.104663 9.18167 0.0498845 9.07775 0.0498047 8.98535C0.0498047 8.89357 0.104125 8.79968 0.225586 8.7373L0.227539 8.73633L2.95605 7.15234H2.95703L2.95898 7.15039C3.09266 7.06212 3.22522 6.97533 3.32324 6.87695C3.42245 6.77735 3.49121 6.66108 3.49121 6.51465V1.80176C3.4913 1.32918 3.78461 0.892215 4.27051 0.571289C4.75607 0.250663 5.42881 0.0498525 6.16992 0.0498047Z" stroke="#484848" stroke-opacity="0.8" stroke-width="0.1"/>
          </svg>
          <span class="icon-name-text">我的</span>
        </div>
      </div>
      <!-- 关于图标 -->
      <div class="about-icon-container" @click="triggerIconFeedback('about')">
        <svg class="about-icon" width="30" height="30" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.605 6.875C9.85625 6.875 10.63 6.755 10.8775 6.5175C10.9973 6.4031 11.092 6.26499 11.1555 6.11197C11.219 5.95895 11.2499 5.79438 11.2463 5.62875C11.2488 5.4642 11.2174 5.30089 11.1539 5.14904C11.0905 4.99719 10.9964 4.86007 10.8775 4.74625C10.7622 4.62949 10.6244 4.5372 10.4726 4.47491C10.3207 4.41263 10.1579 4.38162 9.99375 4.38375C9.83232 4.3817 9.67215 4.41232 9.52285 4.47375C9.37356 4.53519 9.23823 4.62618 9.125 4.74125C9.00625 4.85602 8.91253 4.99411 8.84971 5.14684C8.7869 5.29957 8.75635 5.46365 8.76 5.62875C8.76 5.98875 8.88375 6.28625 9.12875 6.5225C9.375 6.7575 9.36125 6.875 9.605 6.875ZM10 0.625C4.8225 0.625 0.625 4.82125 0.625 10C0.625 15.1788 4.8225 19.375 10 19.375C15.1788 19.375 19.375 15.1788 19.375 10C19.375 4.82125 15.1788 0.625 10 0.625ZM10 18.125C7.84512 18.125 5.77849 17.269 4.25476 15.7452C2.73102 14.2215 1.875 12.1549 1.875 10C1.875 7.84512 2.73102 5.77849 4.25476 4.25476C5.77849 2.73102 7.84512 1.875 10 1.875C12.1549 1.875 14.2215 2.73102 15.7452 4.25476C17.269 5.77849 18.125 7.84512 18.125 10C18.125 12.1549 17.269 14.2215 15.7452 15.7452C14.2215 17.269 12.1549 18.125 10 18.125ZM10.7712 12.685L10.48 12.975L11.2563 8.8575C11.2613 8.83 11.2525 8.8025 11.2537 8.775V8.7625C11.2555 8.67985 11.2401 8.59773 11.2084 8.52135C11.1768 8.44497 11.1297 8.37598 11.07 8.31875L11.065 8.31C11.0613 8.30625 11.0562 8.30625 11.0525 8.3025C10.98 8.23553 10.8932 8.18594 10.7987 8.1575C10.7662 8.1475 10.74 8.12875 10.705 8.12375C10.6813 8.12 10.6575 8.12625 10.6325 8.125L10.5712 8.12625C10.4542 8.12695 10.3393 8.15837 10.2382 8.21738C10.1371 8.27639 10.0532 8.36091 9.995 8.4625L8.31 10.1462C8.24976 10.2066 8.20198 10.2782 8.16941 10.357C8.13684 10.4358 8.1201 10.5202 8.12016 10.6054C8.12022 10.6907 8.13707 10.7751 8.16975 10.8539C8.20243 10.9326 8.2503 11.0041 8.31063 11.0644C8.43246 11.186 8.59763 11.2543 8.76982 11.2542C8.85507 11.2542 8.93948 11.2373 9.01823 11.2046C9.09697 11.1719 9.16851 11.1241 9.22875 11.0637L9.51875 10.7738L8.74375 14.8913C8.73875 14.92 8.7475 14.9462 8.74625 14.975V14.9875C8.74413 15.0702 8.75939 15.1524 8.79104 15.2289C8.82269 15.3053 8.87003 15.3743 8.93 15.4313L8.935 15.4387L8.9475 15.4475C9.02092 15.5129 9.10742 15.5619 9.20125 15.5912C9.2325 15.6012 9.25875 15.6187 9.29375 15.625C9.31875 15.6287 9.3425 15.6212 9.3675 15.6237L9.42875 15.6213C9.54579 15.6211 9.6607 15.59 9.76188 15.5312C9.86306 15.4723 9.94692 15.3879 10.005 15.2863L11.69 13.6012C11.7502 13.5409 11.7979 13.4693 11.8303 13.3906C11.8628 13.3118 11.8795 13.2274 11.8794 13.1422C11.8793 13.057 11.8624 12.9727 11.8297 12.894C11.797 12.8154 11.7491 12.7439 11.6887 12.6838C11.6284 12.6236 11.5568 12.5759 11.4781 12.5434C11.3993 12.5109 11.3149 12.4942 11.2297 12.4944C11.1445 12.4945 11.0602 12.5114 10.9815 12.5441C10.9029 12.5768 10.8314 12.6247 10.7712 12.685Z" fill="#FFFFFF"/>
        </svg>
        <!-- 关于图标名称 -->
        <div class="icon-name-tooltip">
          <svg width="60" height="18" viewBox="0 0 60 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-name-bg">
            <path d="M0.202424 9.30693C-0.0674747 9.12871 -0.0674747 8.83168 0.202424 8.69307L2.9314 7.10891C3.2013 6.93069 3.44121 6.77228 3.44121 6.51485V1.80198C3.44121 0.811881 4.67075 0 6.17019 0H57.271C58.7705 0 60 0.811881 60 1.80198V16.198C60 17.1881 58.7705 18 57.271 18H6.1402C4.64076 18 3.41122 17.1881 3.41122 16.198V11.4653C3.41122 11.2079 3.17131 11.0495 2.90141 10.8713L0.202424 9.30693Z" fill="#2C2C2C"/>
            <path d="M6.16992 0.0498047H57.2715C58.0124 0.0498883 58.6844 0.250804 59.1699 0.571289C59.656 0.892231 59.9501 1.3291 59.9502 1.80176V16.1982C59.9501 16.6709 59.656 17.1078 59.1699 17.4287C58.6844 17.7492 58.0124 17.9501 57.2715 17.9502H6.14062C5.39953 17.9502 4.72682 17.7493 4.24121 17.4287C3.75517 17.1078 3.46103 16.6709 3.46094 16.1982V11.4658C3.46094 11.3194 3.39218 11.2031 3.29297 11.1035C3.19492 11.0051 3.06243 10.9174 2.92871 10.8291L2.92676 10.8281L0.227539 9.26367C0.104663 9.18167 0.0498845 9.07775 0.0498047 8.98535C0.0498047 8.89357 0.104125 8.79968 0.225586 8.7373L0.227539 8.73633L2.95605 7.15234H2.95703L2.95898 7.15039C3.09266 7.06212 3.22522 6.97533 3.32324 6.87695C3.42245 6.77735 3.49121 6.66108 3.49121 6.51465V1.80176C3.4913 1.32918 3.78461 0.892215 4.27051 0.571289C4.75607 0.250663 5.42881 0.0498525 6.16992 0.0498047Z" stroke="#484848" stroke-opacity="0.8" stroke-width="0.1"/>
          </svg>
          <span class="icon-name-text">关于</span>
        </div>
      </div>
      <!-- 设置图标 -->
      <div class="settings-icon-container" @click="triggerIconFeedback('settings')">
        <svg class="settings-icon" width="32" height="32" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15.2937 20.8377H8.68632C7.60465 20.8377 6.59998 20.2584 6.06098 19.3197L2.75365 13.596C2.48879 13.1346 2.34943 12.6119 2.34943 12.0799C2.34943 11.5478 2.48879 11.0251 2.75365 10.5637L6.05732 4.84002C6.32399 4.37954 6.70675 3.99704 7.16741 3.73068C7.62807 3.46432 8.15053 3.32341 8.68265 3.32202H15.2937C15.697 3.32202 16.027 3.65202 16.027 4.05536C16.027 4.45869 15.697 4.78869 15.2937 4.78869H8.68632C8.12898 4.78869 7.60832 5.08935 7.32965 5.57335L4.02598 11.297C3.88903 11.5352 3.81696 11.8051 3.81696 12.0799C3.81696 12.3546 3.88903 12.6245 4.02598 12.8627L7.32965 18.5864C7.4673 18.8245 7.66505 19.0223 7.90313 19.16C8.14122 19.2977 8.41128 19.3705 8.68632 19.371H15.2973C15.8547 19.371 16.3753 19.0704 16.654 18.5864L19.9577 12.8627C20.2363 12.3787 20.2363 11.781 19.9577 11.297L17.435 6.93002C17.2333 6.57802 17.3543 6.13069 17.7027 5.92902C18.0547 5.72735 18.502 5.84836 18.7037 6.19669L21.2227 10.5637C21.4875 11.0251 21.6269 11.5478 21.6269 12.0799C21.6269 12.6119 21.4875 13.1346 21.2227 13.596L17.919 19.3197C17.6527 19.7805 17.27 20.1633 16.8093 20.4297C16.3485 20.6961 15.8259 20.8368 15.2937 20.8377Z" fill="#FFFFFF"/>
          <path d="M11.99 15.5908C10.054 15.5908 8.48102 14.0178 8.48102 12.0818C8.48102 10.1458 10.054 8.57275 11.99 8.57275C13.926 8.57275 15.499 10.1458 15.499 12.0818C15.499 14.0178 13.926 15.5908 11.99 15.5908ZM11.99 10.0358C10.8644 10.0358 9.94768 10.9524 9.94768 12.0781C9.94768 13.2038 10.8644 14.1204 11.99 14.1204C13.1157 14.1204 14.0324 13.2038 14.0324 12.0781C14.0324 10.9524 13.1157 10.0358 11.99 10.0358Z" fill="#FFFFFF"/>
        </svg>
        <!-- 设置图标名称 -->
        <div class="icon-name-tooltip">
          <svg width="60" height="18" viewBox="0 0 60 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-name-bg">
            <path d="M0.202424 9.30693C-0.0674747 9.12871 -0.0674747 8.83168 0.202424 8.69307L2.9314 7.10891C3.2013 6.93069 3.44121 6.77228 3.44121 6.51485V1.80198C3.44121 0.811881 4.67075 0 6.17019 0H57.271C58.7705 0 60 0.811881 60 1.80198V16.198C60 17.1881 58.7705 18 57.271 18H6.1402C4.64076 18 3.41122 17.1881 3.41122 16.198V11.4653C3.41122 11.2079 3.17131 11.0495 2.90141 10.8713L0.202424 9.30693Z" fill="#2C2C2C"/>
            <path d="M6.16992 0.0498047H57.2715C58.0124 0.0498883 58.6844 0.250804 59.1699 0.571289C59.656 0.892231 59.9501 1.3291 59.9502 1.80176V16.1982C59.9501 16.6709 59.656 17.1078 59.1699 17.4287C58.6844 17.7492 58.0124 17.9501 57.2715 17.9502H6.14062C5.39953 17.9502 4.72682 17.7493 4.24121 17.4287C3.75517 17.1078 3.46103 16.6709 3.46094 16.1982V11.4658C3.46094 11.3194 3.39218 11.2031 3.29297 11.1035C3.19492 11.0051 3.06243 10.9174 2.92871 10.8291L2.92676 10.8281L0.227539 9.26367C0.104663 9.18167 0.0498845 9.07775 0.0498047 8.98535C0.0498047 8.89357 0.104125 8.79968 0.225586 8.7373L0.227539 8.73633L2.95605 7.15234H2.95703L2.95898 7.15039C3.09266 7.06212 3.22522 6.97533 3.32324 6.87695C3.42245 6.77735 3.49121 6.66108 3.49121 6.51465V1.80176C3.4913 1.32918 3.78461 0.892215 4.27051 0.571289C4.75607 0.250663 5.42881 0.0498525 6.16992 0.0498047Z" stroke="#484848" stroke-opacity="0.8" stroke-width="0.1"/>
          </svg>
          <span class="icon-name-text">设置</span>
        </div>
      </div>
      <!-- 注销图标 -->
      <div class="logout-icon-container" @click="triggerIconFeedback('logout')">
        <svg class="logout-icon" width="32" height="32" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.23397 18.919H3.95311C3.46757 18.919 3.07225 18.5237 3.07225 18.0382V3.95947C3.07225 3.47393 3.46757 3.07861 3.95311 3.07861H9.23397C9.71952 3.07861 10.1148 2.68545 10.1148 2.19775C10.1148 1.71006 9.72167 1.31689 9.23397 1.31689H3.95311C2.49647 1.31689 1.31268 2.50068 1.31268 3.95732V18.0382C1.31268 19.4948 2.49647 20.6786 3.95311 20.6786H9.23397C9.71952 20.6786 10.1148 20.2855 10.1148 19.7978C10.1148 19.3101 9.71952 18.919 9.23397 18.919Z" fill="#FFFFFF"/>
          <path d="M20.6852 11.0301C20.6852 11.0322 20.6873 11.0322 20.6852 11.0301ZM20.6852 11.0301C20.6873 10.9721 20.6809 10.9119 20.668 10.8561C20.6658 10.8518 20.6637 10.8453 20.6637 10.841C20.6293 10.6820 20.5520 10.5316 20.4295 10.4092C20.4252 10.4049 20.4209 10.4027 20.4188 10.4006L16.0746 6.05430C15.7330 5.71270 15.1723 5.71270 14.8307 6.05430C14.4891 6.39590 14.4891 6.95664 14.8307 7.29824L17.6816 10.1514H6.60645C6.12305 10.1514 5.72559 10.5467 5.72559 11.0322C5.72559 11.5156 6.12090 11.9131 6.60645 11.9131H17.6816L14.8285 14.7641C14.4869 15.1057 14.4869 15.6664 14.8285 16.0080C15.1701 16.3496 15.7309 16.3496 16.0725 16.0080L20.4166 11.6639C20.4209 11.6596 20.4252 11.6574 20.4273 11.6553C20.5498 11.5328 20.6271 11.3824 20.6615 11.2234C20.6637 11.2191 20.6658 11.2127 20.6658 11.2084C20.6809 11.1482 20.6873 11.0902 20.6852 11.0301Z" fill="#FFFFFF"/>
        </svg>
        <!-- 注销图标名称 -->
        <div class="icon-name-tooltip">
          <svg width="60" height="18" viewBox="0 0 60 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-name-bg">
            <path d="M0.202424 9.30693C-0.0674747 9.12871 -0.0674747 8.83168 0.202424 8.69307L2.9314 7.10891C3.2013 6.93069 3.44121 6.77228 3.44121 6.51485V1.80198C3.44121 0.811881 4.67075 0 6.17019 0H57.271C58.7705 0 60 0.811881 60 1.80198V16.198C60 17.1881 58.7705 18 57.271 18H6.1402C4.64076 18 3.41122 17.1881 3.41122 16.198V11.4653C3.41122 11.2079 3.17131 11.0495 2.90141 10.8713L0.202424 9.30693Z" fill="#2C2C2C"/>
            <path d="M6.16992 0.0498047H57.2715C58.0124 0.0498883 58.6844 0.250804 59.1699 0.571289C59.656 0.892231 59.9501 1.3291 59.9502 1.80176V16.1982C59.9501 16.6709 59.656 17.1078 59.1699 17.4287C58.6844 17.7492 58.0124 17.9501 57.2715 17.9502H6.14062C5.39953 17.9502 4.72682 17.7493 4.24121 17.4287C3.75517 17.1078 3.46103 16.6709 3.46094 16.1982V11.4658C3.46094 11.3194 3.39218 11.2031 3.29297 11.1035C3.19492 11.0051 3.06243 10.9174 2.92871 10.8291L2.92676 10.8281L0.227539 9.26367C0.104663 9.18167 0.0498845 9.07775 0.0498047 8.98535C0.0498047 8.89357 0.104125 8.79968 0.225586 8.7373L0.227539 8.73633L2.95605 7.15234H2.95703L2.95898 7.15039C3.09266 7.06212 3.22522 6.97533 3.32324 6.87695C3.42245 6.77735 3.49121 6.66108 3.49121 6.51465V1.80176C3.4913 1.32918 3.78461 0.892215 4.27051 0.571289C4.75607 0.250663 5.42881 0.0498525 6.16992 0.0498047Z" stroke="#484848" stroke-opacity="0.8" stroke-width="0.1"/>
          </svg>
          <span class="icon-name-text">注销</span>
        </div>
      </div>
    </div>
    <div class="title-bar" data-tauri-drag-region="true">
      <div class="window-controls">
        <div class="minimize-button" @click="minimizeWindow">
          <div class="minimize-icon"></div>
        </div>
        <div class="maximize-button" @click="maximizeWindow">
          <div class="maximize-icon"></div>
        </div>
        <div class="close-button" @click="closeWindow">
          <div class="close-icon"></div>
        </div>
      </div>
    </div>
    <div class="showcase-area">
      <main class="container">
      </main>
      <ParameterPanel />
      <CreationStatusPanel />
      <WorkPanel />
      <FileManager @fileSelect="handleFileSelect" />
    </div>
  </div>
</template>

<style scoped>
.logo.vite:hover {
  filter: drop-shadow(0 0 2em #747bff);
}

.logo.vue:hover {
  filter: drop-shadow(0 0 2em #249b73);
}

</style>
<style>
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  background-color: #000000 !important;
  border: none !important; /* 强制移除任何边框 */
}

:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color: #f6f6f6;
  background-color: #000000;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

.background {
  width: 100vw;
  height: 100vh;
  background-color: #000000;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  /* 移除过渡效果以避免卡顿 */
  /* 确保背景色优先级最高 */
  background-color: #000000 !important;
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: width, height;
}

.sidebar {
  position: absolute;
  left: 0;
  top: 0;
  width: 55px;
  height: 100vh;
  background-color: #000000;
  border-right: 0.1px solid #3F3F3F;
  z-index: 10;
  /* 使用 transform3d 触发硬件加速 */
  transform: translate3d(0, 0, 0);
  will-change: height;
  /* 添加硬件加速优化 */
  backface-visibility: hidden;
  perspective: 1000px;
}

.app-logo-container {
  position: absolute;
  top: 17px; /* 居中调整 */
  left: 7px; /* 居中调整 */
  width: 42px;
  height: 42px;
}

.app-logo {
  width: 42px;
  height: 42px;
  background-color: #5825BD;
  border-radius: 50%;
  position: relative;
}

.app-logo::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0) 100%);
  border-radius: 50%;
  z-index: -1;
}

/* 主页图标容器 */
.home-icon-container {
  position: absolute;
  top: 117px; /* 调整位置以适应放大后的图标 */
  left: 12.5px; /* 居中对齐 */
  width: 30px;
  height: 30px;
  cursor: pointer;
}

/* 主页图标样式 */
.home-icon {
  width: 30px;
  height: 30px;
}

/* 主页图标悬停效果 */
.home-icon:hover {
  filter: drop-shadow(0 0 2em rgba(255, 255, 255, 0.5));
}

/* 主页图标点击反馈效果 */
.home-icon-container:active .home-icon {
  transform: scale(0.9);
  transition: transform 0.1s ease;
}

/* 主页图标名称容器 */
.icon-name-tooltip {
  position: absolute;
  left: 45px; /* 图标右侧 */
  top: 50%;
  transform: translateY(-50%); /* 垂直居中 */
  opacity: 0; /* 默认隐藏 */
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease; /* 添加过渡效果 */
  z-index: 100;
}

/* 主页图标悬停时显示名称 */
.home-icon-container:hover .icon-name-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 图标名称背景 */
.icon-name-bg {
  width: 60px;
  height: 18px;
  display: block;
}

/* 图标名称文本 */
.icon-name-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  white-space: nowrap;
  pointer-events: none; /* 文本不响应鼠标事件 */
}

/* 选中框容器 */
.selection-box-container {
  position: absolute;
  top: 117px; /* 调整位置以适应放大后的图标 */
  left: 7.5px; /* 居中对齐 */
  width: 40px;
  height: 40px;
  pointer-events: none; /* 选中框不响应鼠标事件 */
}

/* 选中框样式 */
.selection-box {
  width: 40px;
  height: 40px;
}

/* 工作图标容器 */
.work-icon-container {
  position: absolute;
  top: 182px; /* 调整位置以适应放大后的图标 */
  left: 15px; /* 居中对齐 */
  width: 29px;
  height: 29px;
  cursor: pointer;
}

/* 工作图标样式 */
.work-icon {
  width: 29px;
  height: 29px;
}

/* 工作图标悬停效果 */
.work-icon:hover {
  filter: drop-shadow(0 0 2em rgba(255, 255, 255, 0.5));
}

/* 工作图标悬停时显示名称 */
.work-icon-container:hover .icon-name-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 工作图标点击反馈效果 */
.work-icon-container:active .work-icon {
  transform: scale(0.9);
  transition: transform 0.1s ease;
}

/* 书架图标容器 */
.bookshelf-icon-container {
  position: absolute;
  top: 247px; /* 调整位置以适应图标 */
  left: 13.5px; /* 居中对齐 */
  width: 28px;
  height: 28px;
  cursor: pointer;
}

/* 书架图标样式 */
.bookshelf-icon {
  width: 28px;
  height: 28px;
}

/* 书架图标悬停效果 */
.bookshelf-icon:hover {
  filter: drop-shadow(0 0 2em rgba(255, 255, 255, 0.5));
}

/* 书架图标悬停时显示名称 */
.bookshelf-icon-container:hover .icon-name-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 书架图标点击反馈效果 */
.bookshelf-icon-container:active .bookshelf-icon {
  transform: scale(0.9);
  transition: transform 0.1s ease;
}

/* 统计图标容器 */
.statistics-icon-container {
  position: absolute;
  top: 312px; /* 调整位置以适应图标 */
  left: 11px; /* 居中对齐 */
  width: 33px;
  height: 33px;
  cursor: pointer;
}

/* 统计图标样式 */
.statistics-icon {
  width: 33px;
  height: 33px;
}

/* 统计图标悬停效果 */
.statistics-icon:hover {
  filter: drop-shadow(0 0 2em rgba(255, 255, 255, 0.5));
}

/* 统计图标悬停时显示名称 */
.statistics-icon-container:hover .icon-name-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 统计图标点击反馈效果 */
.statistics-icon-container:active .statistics-icon {
  transform: scale(0.9);
  transition: transform 0.1s ease;
}

/* 我的图标容器 */
.my-icon-container {
  position: absolute;
  top: 377px; /* 调整位置以适应图标 */
  left: 10px; /* 居中对齐 */
  width: 35px;
  height: 34px;
  cursor: pointer;
}

/* 我的图标样式 */
.my-icon {
  width: 35px;
  height: 34px;
}

/* 我的图标悬停效果 */
.my-icon:hover {
  filter: drop-shadow(0 0 2em rgba(255, 255, 255, 0.5));
}

/* 我的图标悬停时显示名称 */
.my-icon-container:hover .icon-name-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 我的图标点击反馈效果 */
.my-icon-container:active .my-icon {
  transform: scale(0.9);
  transition: transform 0.1s ease;
}

/* 关于图标容器 */
.about-icon-container {
  position: absolute;
  bottom: 110px; /* 调整位置以确保与注销图标间距为65px */
  left: 15px; /* 居中对齐 */
  width: 25px;
  height: 25px;
  cursor: pointer;
}

/* 关于图标样式 */
.about-icon {
  width: 25px;
  height: 25px;
}

/* 关于图标悬停效果 */
.about-icon:hover {
  filter: drop-shadow(0 0 2em rgba(255, 255, 255, 0.5));
}

/* 关于图标悬停时显示名称 */
.about-icon-container:hover .icon-name-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 关于图标点击反馈效果 */
.about-icon-container:active .about-icon {
  transform: scale(0.9);
  transition: transform 0.1s ease;
}

/* 设置图标容器 */
.settings-icon-container {
  position: absolute;
  bottom: 175px; /* 使用bottom属性确保与关于图标间距为65px */
  left: 14px; /* 居中对齐 */
  width: 27px;
  height: 27px;
  cursor: pointer;
}

/* 设置图标样式 */
.settings-icon {
  width: 27px;
  height: 27px;
}

/* 设置图标悬停效果 */
.settings-icon:hover {
  filter: drop-shadow(0 0 2em rgba(255, 255, 255, 0.5));
}

/* 设置图标悬停时显示名称 */
.settings-icon-container:hover .icon-name-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 设置图标点击反馈效果 */
.settings-icon-container:active .settings-icon {
  transform: scale(0.9);
  transition: transform 0.1s ease;
}

/* 注销图标容器 */
.logout-icon-container {
  position: absolute;
  bottom: 30px; /* 距离底部30px */
  left: 15.5px; /* 居中对齐 */
  width: 14px;
  height: 14px;
  cursor: pointer;
}

/* 注销图标样式 */
.logout-icon {
  width: 24px;
  height: 24px;
}

/* 注销图标悬停效果 */
.logout-icon:hover {
  filter: drop-shadow(0 0 2em rgba(255, 255, 255, 0.5));
}

/* 注销图标悬停时显示名称 */
.logout-icon-container:hover .icon-name-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 注销图标点击反馈效果 */
.logout-icon-container:active .logout-icon {
  transform: scale(0.9);
  transition: transform 0.1s ease;
}

.title-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 30px; /* Further reduced height */
  -webkit-app-region: drag;
  z-index: 15;
  background-color: transparent;
}


.window-controls {
  -webkit-app-region: no-drag;
  position: absolute;
  top: 7px; /* Adjusted for 16px buttons */
  right: 17.5px;
  z-index: 20;
  display: flex;
  gap: 10px; /* Space between buttons */
}

/* 最大化按钮 */
.maximize-button {
  width: 16px;
  height: 16px;
  background: linear-gradient(to bottom, #FFCC00 0%, #FFCC00 100%);
  border-radius: 50%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  position: relative;
  cursor: pointer;
  z-index: 9999;
}

.maximize-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 9.65px; /* Adjusted for 16px button */
  height: 9.65px;
  border: 1px solid rgba(0, 0, 0, 0.8);
  box-sizing: border-box;
}

/* 最小化按钮 */
.minimize-button {
  width: 16px;
  height: 16px;
  background: linear-gradient(to bottom, #00FF55 0%, #00FF55 100%);
  border-radius: 50%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  position: relative;
  cursor: pointer;
  z-index: 9999;
}

.minimize-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12.71px; /* Adjusted for 16px button */
  height: 0px;
  border-top: 1px solid rgba(0, 0, 0, 0.8);
}

/* 关闭按钮 */
.close-button {
  width: 16px;
  height: 16px;
  background: linear-gradient(to bottom, #D83F3F 0%, #D83F3F 100%);
  border-radius: 50%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  position: relative;
  cursor: pointer;
}

.close-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 9.05px; /* Adjusted for 16px button */
  height: 9.05px;
}

.close-icon::before,
.close-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.8);
}

.close-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.close-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.showcase-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw; /* 使用视口宽度 */
  height: 100vh; /* 使用视口高度 */
  background: linear-gradient(to bottom, rgba(217, 217, 217, 0.15) 0%, rgba(255, 254, 254, 0) 100%);
  backdrop-filter: blur(80px);
  -webkit-backdrop-filter: blur(80px);
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  overflow: hidden;
  /* 移除过渡效果以避免卡顿 */
  /* 使用硬件加速 */
  transform: translateZ(0);
  will-change: width, height;
}

.container {
  margin: 0;
  padding-top: 10vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: 0.75s;
}

.logo.tauri:hover {
  filter: drop-shadow(0 0 2em #24c8db);
}

.row {
  display: flex;
  justify-content: center;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

h1 {
  text-align: center;
}

input,
button {
  border-radius: 8px;
  border: 1px solid #3c3c3c;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  color: #f6f6f6;
  background-color: #1a1a1a;
  transition: border-color 0.25s;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
}

button {
  cursor: pointer;
}

button:hover {
  border-color: #535bf2;
}
button:active {
  border-color: #535bf2;
  background-color: #2a2a2a;
}

input,
button {
  outline: none;
}

#greet-input {
  margin-right: 5px;
}


</style>
