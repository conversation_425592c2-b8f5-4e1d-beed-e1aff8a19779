<template>
  <div class="parameter-panel" :class="{ 'maximized': isMaximized, 'normal': !isMaximized }">
    <!-- 主容器背景 -->
    <div class="panel-background"></div>

    <!-- 状态指示器 -->
    <div class="status-indicator">
      <div class="status-dot"></div>
      <span class="status-text">就绪</span>
    </div>

    <!-- 标题 -->
    <h2 class="panel-title">创作参数</h2>

    <!-- 设置按钮 -->
    <button class="settings-button" @click="openSettings">
      <svg width="16" height="16" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M23.2051 20.068L6.26632 20.1141C6.08692 19.4384 5.68936 18.8411 5.13569 18.4157C4.58202 17.9902 3.90339 17.7604 3.20571 17.7621C1.46302 17.7669 0.0520556 19.1812 0.0567832 20.9173C0.0615109 22.6534 1.48016 24.0615 3.22135 24.0567C4.68856 24.0528 5.92396 23.044 6.27061 21.6881L23.2094 21.642C23.3167 21.6483 23.4242 21.6326 23.5252 21.5957C23.6262 21.5589 23.7186 21.5017 23.7967 21.4277C23.8749 21.3537 23.937 21.2644 23.9794 21.1654C24.0218 21.0664 24.0436 20.9597 24.0433 20.852C24.043 20.7442 24.0207 20.6377 23.9777 20.5389C23.9348 20.4401 23.8721 20.3512 23.7936 20.2776C23.7151 20.204 23.6224 20.1473 23.5211 20.111C23.4199 20.0747 23.3124 20.0595 23.2051 20.0665L23.2051 20.068ZM3.21706 22.4827C2.34722 22.4851 1.63715 21.7818 1.63478 20.913C1.63242 20.0457 2.33865 19.337 3.20849 19.3346C4.07983 19.3323 4.78841 20.0356 4.79077 20.9044C4.79314 21.7717 4.08841 22.4803 3.21706 22.4827ZM23.1959 11.2119L20.7631 11.2186C20.5841 10.5421 20.1865 9.94405 19.6325 9.51802C19.0785 9.09199 18.3993 8.86195 17.701 8.86385C17.0027 8.86575 16.3247 9.09949 15.773 9.52853C15.2213 9.95757 14.827 10.5577 14.6517 11.2352L0.922828 11.2726C0.81941 11.2725 0.716983 11.2928 0.621396 11.3323C0.525808 11.3719 0.438931 11.4299 0.365725 11.5031C0.29252 11.5764 0.234419 11.6633 0.19474 11.759C0.155062 11.8547 0.134583 11.9574 0.134473 12.061C0.135658 12.4961 0.489942 12.8463 0.925613 12.8451L14.6545 12.8077C14.8329 13.4849 15.2304 14.0836 15.7846 14.5102C16.3388 14.9368 17.0186 15.1672 17.7174 15.1653C18.4162 15.1634 19.0946 14.9293 19.6465 14.4997C20.1984 14.0701 20.5926 13.4692 20.7674 12.7911L23.2002 12.7845C23.3073 12.7908 23.4146 12.7751 23.5155 12.7383C23.6163 12.7015 23.7085 12.6444 23.7865 12.5706C23.8645 12.4967 23.9265 12.4076 23.9689 12.3088C24.0112 12.2099 24.0329 12.1035 24.0326 11.9959C24.0323 11.8884 24.01 11.782 23.9672 11.6834C23.9243 11.5848 23.8618 11.4961 23.7834 11.4226C23.705 11.3492 23.6124 11.2926 23.5114 11.2564C23.4104 11.2201 23.303 11.205 23.1959 11.2119ZM17.7138 13.5872C16.844 13.5895 16.1339 12.8862 16.1315 12.0159C16.1292 11.1486 16.8354 10.4415 17.7052 10.4391C18.5751 10.4367 19.2852 11.1401 19.2875 12.0088C19.2899 12.8761 18.5837 13.5848 17.7138 13.5872ZM3.17311 6.34325C4.64032 6.33926 5.87573 5.33054 6.22237 3.97463L23.1611 3.9285C23.2672 3.93286 23.373 3.91565 23.4722 3.87792C23.5714 3.84019 23.6619 3.78272 23.7384 3.70897C23.8148 3.63523 23.8756 3.54673 23.917 3.44881C23.9584 3.3509 23.9796 3.2456 23.9793 3.13925C23.979 3.03291 23.9572 2.92772 23.9153 2.83004C23.8734 2.73235 23.8121 2.64419 23.7353 2.57086C23.6584 2.49753 23.5676 2.44055 23.4681 2.40336C23.3687 2.36617 23.2628 2.34954 23.1568 2.35447L6.21808 2.4006C6.01948 1.66055 5.55981 1.01781 4.92433 0.591617C4.28885 0.16542 3.5207 -0.0153076 2.76238 0.0829613C2.00407 0.18123 1.30705 0.551825 0.800641 1.126C0.294231 1.70017 0.0127978 2.43895 0.00855037 3.20528C0.013274 4.93987 1.43193 6.34799 3.17311 6.34325ZM3.16026 1.62265C4.0316 1.62028 4.74018 2.3236 4.74254 3.19239C4.7449 4.05968 4.04017 4.76685 3.16883 4.76922C2.29898 4.77159 1.58891 4.06828 1.58655 3.20098C1.58418 2.33069 2.29041 1.62352 3.16025 1.62115L3.16026 1.62265Z" fill="rgba(242, 230, 230, 0.49)"/>
      </svg>
    </button>

    <!-- 分隔线 -->
    <div class="separator-line"></div>

    <!-- 自定义滚动条 -->
    <div class="custom-scrollbar" v-show="showScrollbar">
      <div
        class="scrollbar-track"
        @click="handleTrackClick"
        ref="scrollbarTrack"
      >
        <div
          class="scrollbar-thumb"
          :style="{
            height: thumbHeight + 'px',
            top: thumbTop + 'px'
          }"
          @mousedown="startDrag"
        ></div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="panel-content" @scroll="handleContentScroll">
      <form class="creation-form" @submit.prevent="handleSubmit">
        <!-- 书名 -->
        <div class="form-group">
          <label for="bookTitle">书名</label>
          <input
            type="text"
            id="bookTitle"
            v-model="formData.bookTitle"
            placeholder="请输入您的作品名称"
            class="form-input"
          />
        </div>

        <!-- 创作灵感 -->
        <div class="form-group">
          <label for="inspiration">创作灵感</label>
          <textarea
            id="inspiration"
            v-model="formData.inspiration"
            placeholder="描述您的创作灵感和想法..."
            class="form-textarea"
            rows="3"
          ></textarea>
        </div>

        <!-- 题材 -->
        <div class="form-group">
          <label for="genre">题材</label>
          <select id="genre" v-model="formData.genre" class="form-select">
            <option value="">请选择题材</option>
            <option value="fantasy">玄幻</option>
            <option value="urban">都市</option>
            <option value="romance">言情</option>
            <option value="historical">历史</option>
            <option value="scifi">科幻</option>
            <option value="military">军事</option>
            <option value="game">游戏</option>
            <option value="sports">体育</option>
            <option value="suspense">悬疑</option>
            <option value="other">其他</option>
          </select>
        </div>

        <!-- 网文小说类型 -->
        <div class="form-group">
          <label for="novelType">网文小说类型</label>
          <select id="novelType" v-model="formData.novelType" class="form-select">
            <option value="">请选择类型</option>
            <option value="male">男频</option>
            <option value="female">女频</option>
            <option value="general">通用</option>
          </select>
        </div>

        <!-- 单章节字数 -->
        <div class="form-group">
          <label for="chapterWords">单章节字数</label>
          <input
            type="number"
            id="chapterWords"
            v-model="formData.chapterWords"
            placeholder="建议2000-5000字"
            class="form-input"
            min="500"
            max="10000"
          />
        </div>

        <!-- 模式选择 -->
        <div class="form-group">
          <label>模式选择</label>
          <div class="mode-options">
            <label class="mode-option" :class="{ active: formData.mode === 'short' }">
              <input type="radio" v-model="formData.mode" value="short" />
              <span class="mode-label">短篇模式</span>
              <span class="mode-desc">1-5万字</span>
            </label>
            <label class="mode-option" :class="{ active: formData.mode === 'medium' }">
              <input type="radio" v-model="formData.mode" value="medium" />
              <span class="mode-label">中篇模式</span>
              <span class="mode-desc">5-20万字</span>
            </label>
            <label class="mode-option" :class="{ active: formData.mode === 'long' }">
              <input type="radio" v-model="formData.mode" value="long" />
              <span class="mode-label">长篇模式</span>
              <span class="mode-desc">20万字以上</span>
            </label>
            <label class="mode-option" :class="{ active: formData.mode === 'adaptive' }">
              <input type="radio" v-model="formData.mode" value="adaptive" />
              <span class="mode-label">自适应模式</span>
              <span class="mode-desc">根据内容自动调整</span>
            </label>
          </div>
        </div>

        <!-- 提交按钮 -->
        <div class="form-actions">
          <button type="submit" class="submit-btn">开始创作</button>
          <button type="button" class="reset-btn" @click="resetForm">重置</button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, nextTick } from 'vue';
import { getCurrentWindow } from '@tauri-apps/api/window';

// 窗口最大化状态
const isMaximized = ref(false);

// 滚动条相关状态
const showScrollbar = ref(false);
const thumbHeight = ref(50);
const thumbTop = ref(0);
const isDragging = ref(false);
const dragStartY = ref(0);
const dragStartScrollTop = ref(0);

// DOM引用
const scrollbarTrack = ref<HTMLElement | null>(null);

// 表单数据
const formData = reactive({
  bookTitle: '',
  inspiration: '',
  genre: '',
  novelType: '',
  chapterWords: 3000,
  mode: 'adaptive'
});

const openSettings = () => {
  // 打开设置的逻辑
  console.log('打开设置面板');
};

// 表单提交处理
const handleSubmit = () => {
  console.log('创作参数:', formData);
  // 这里可以添加表单验证和提交逻辑
  if (!formData.bookTitle.trim()) {
    alert('请输入书名');
    return;
  }
  if (!formData.inspiration.trim()) {
    alert('请输入创作灵感');
    return;
  }
  if (!formData.genre) {
    alert('请选择题材');
    return;
  }
  if (!formData.novelType) {
    alert('请选择网文小说类型');
    return;
  }

  alert('创作参数设置完成！开始创作...');
};

// 重置表单
const resetForm = () => {
  formData.bookTitle = '';
  formData.inspiration = '';
  formData.genre = '';
  formData.novelType = '';
  formData.chapterWords = 3000;
  formData.mode = 'adaptive';
};

// 获取内容元素
const getContentElement = (): HTMLElement | null => {
  return document.querySelector('.panel-content');
};

// 更新滚动条状态
const updateScrollbar = () => {
  nextTick(() => {
    const contentEl = getContentElement();
    if (!contentEl) return;

    const { scrollTop, scrollHeight, clientHeight } = contentEl;

    // 检查是否需要显示滚动条
    showScrollbar.value = scrollHeight > clientHeight;

    if (showScrollbar.value && scrollbarTrack.value) {
      const trackHeight = scrollbarTrack.value.clientHeight;

      // 计算滚动条拇指的高度
      const thumbHeightRatio = clientHeight / scrollHeight;
      thumbHeight.value = Math.max(trackHeight * thumbHeightRatio, 20); // 最小高度20px

      // 确保拇指高度不超过轨道高度
      thumbHeight.value = Math.min(thumbHeight.value, trackHeight);

      // 计算滚动条拇指的位置
      const maxScrollTop = scrollHeight - clientHeight;
      const scrollRatio = maxScrollTop > 0 ? scrollTop / maxScrollTop : 0;
      const maxThumbTop = trackHeight - thumbHeight.value;

      // 确保拇指位置不超出轨道范围
      thumbTop.value = Math.max(0, Math.min(scrollRatio * maxThumbTop, maxThumbTop));
    }
  });
};

// 处理内容滚动
const handleContentScroll = () => {
  if (!isDragging.value) {
    updateScrollbar();
  }
};

// 处理轨道点击
const handleTrackClick = (event: MouseEvent) => {
  const contentEl = getContentElement();
  if (!contentEl || !scrollbarTrack.value) return;

  const trackRect = scrollbarTrack.value.getBoundingClientRect();
  const clickY = event.clientY - trackRect.top;
  const trackHeight = scrollbarTrack.value.clientHeight;

  // 计算点击位置对应的滚动比例
  const clickRatio = clickY / trackHeight;
  const maxScrollTop = contentEl.scrollHeight - contentEl.clientHeight;

  // 设置滚动位置
  contentEl.scrollTop = clickRatio * maxScrollTop;
  updateScrollbar();
};

// 开始拖拽
const startDrag = (event: MouseEvent) => {
  const contentEl = getContentElement();
  if (!contentEl) return;

  isDragging.value = true;
  dragStartY.value = event.clientY;
  dragStartScrollTop.value = contentEl.scrollTop;

  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
  event.preventDefault();
};

// 处理拖拽
const handleDrag = (event: MouseEvent) => {
  if (!isDragging.value || !scrollbarTrack.value) return;

  const contentEl = getContentElement();
  if (!contentEl) return;

  const deltaY = event.clientY - dragStartY.value;
  const trackHeight = scrollbarTrack.value.clientHeight;
  const maxScrollTop = contentEl.scrollHeight - contentEl.clientHeight;

  // 计算拖拽比例
  const dragRatio = deltaY / trackHeight;
  const newScrollTop = dragStartScrollTop.value + (dragRatio * maxScrollTop);

  // 限制滚动范围
  contentEl.scrollTop = Math.max(0, Math.min(newScrollTop, maxScrollTop));
  updateScrollbar();
};

// 停止拖拽
const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
};



// 检查窗口最大化状态
const checkMaximizedState = async () => {
  try {
    const window = getCurrentWindow();
    const wasMaximized = isMaximized.value;
    isMaximized.value = await window.isMaximized();

    // 如果窗口状态发生变化，重新计算滚动条
    if (wasMaximized !== isMaximized.value) {
      setTimeout(() => {
        updateScrollbar();
      }, 100); // 延迟一点时间等待CSS过渡完成
    }
  } catch (error) {
    console.error('检查窗口状态失败:', error);
  }
};

onMounted(async () => {
  await checkMaximizedState();

  // 初始化滚动条
  setTimeout(() => {
    updateScrollbar();
  }, 100);

  // 监听窗口状态变化
  const window = getCurrentWindow();
  const unlisten = await window.onResized(() => {
    checkMaximizedState();
    updateScrollbar();
  });

  // 组件卸载时清理监听器
  onUnmounted(() => {
    unlisten();
    stopDrag(); // 清理拖拽事件
  });
});
</script>

<style scoped>
.parameter-panel {
  position: absolute;
  transition: all 0.3s ease-in-out;
}

/* 最大化状态 - 大尺寸 */
.parameter-panel.maximized {
  top: 58px;
  left: 113px;
  width: 774px;
  height: 550px;
}

/* 普通状态 - 小尺寸，保持与最大化状态相同的布局比例 */
.parameter-panel.normal {
  top: 40px; /* 58px * 0.69 ≈ 40px */
  left: 78px; /* 113px * 0.69 ≈ 78px */
  width: 534px; /* 774px * 0.69 ≈ 534px */
  height: 380px; /* 550px * 0.69 ≈ 380px */
}

/* 主背景容器 */
.panel-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0);
  border: 1px solid #3B3B3B;
  border-radius: 20px;
  box-sizing: border-box;
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 4.4%; /* 24px / 550px ≈ 4.4% */
  left: 2.5%; /* 19px / 774px ≈ 2.5% */
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  background-color: #0DFF00;
  border-radius: 50%;
  box-shadow: 0 0 6px rgba(13, 255, 0, 0.5);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 6px rgba(13, 255, 0, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 12px rgba(13, 255, 0, 0.8);
    transform: scale(1.1);
  }
  100% {
    box-shadow: 0 0 6px rgba(13, 255, 0, 0.5);
    transform: scale(1);
  }
}

.status-text {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.21;
  color: #FFFFFF;
  text-align: left;
}

/* 标题 */
.panel-title {
  position: absolute;
  top: 3.1%; /* 17px / 550px ≈ 3.1% */
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  height: 24px;
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 20px;
  font-weight: 400;
  line-height: 1.21;
  color: #FFFFFF;
  text-align: center;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 设置按钮 */
.settings-button {
  position: absolute;
  top: 3.6%; /* 20px / 550px ≈ 3.6% */
  right: 4.3%; /* 33px / 774px ≈ 4.3% */
  width: 16px;
  height: 16px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.settings-button:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* 分隔线 */
.separator-line {
  position: absolute;
  top: 11.6%; /* 64px / 550px ≈ 11.6% */
  left: 0.1%; /* 0.81px / 774px ≈ 0.1% */
  width: 99.5%; /* 770px / 774px ≈ 99.5% */
  height: 0px;
  border-top: 1.2px solid rgba(255, 255, 255, 0.09);
}

/* 自定义滚动条 */
.custom-scrollbar {
  position: absolute;
  top: 15%; /* 从内容区域开始 */
  right: 1.4%; /* 11px / 774px ≈ 1.4% */
  width: 8px;
  bottom: 5%; /* 到内容区域结束 */
  z-index: 10;
}

.scrollbar-track {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(138, 138, 138, 0.3);
  border: 1px solid rgba(138, 138, 138, 0.2);
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.scrollbar-track:hover {
  background: rgba(138, 138, 138, 0.4);
}

.scrollbar-thumb {
  position: absolute;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 4px;
  cursor: grab;
  transition: background-color 0.2s ease;
  min-height: 20px;
}

.scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.8);
}

.scrollbar-thumb:active {
  cursor: grabbing;
  background: rgba(13, 255, 0, 0.7);
}

/* 内容区域 */
.panel-content {
  position: absolute;
  top: 14.5%; /* 80px / 550px ≈ 14.5% */
  left: 2.6%; /* 20px / 774px ≈ 2.6% */
  right: 2.6%; /* 20px / 774px ≈ 2.6% */
  bottom: 3.6%; /* 20px / 550px ≈ 3.6% */
  color: #FFFFFF;
  overflow-y: auto;
  padding: 0;
}

.panel-content::-webkit-scrollbar {
  display: none;
}

.panel-content {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* 表单样式 */
.creation-form {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 13px;
  font-weight: 500;
  color: #FFFFFF;
  margin-bottom: 4px;
}

.form-input,
.form-textarea,
.form-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px 12px;
  color: #FFFFFF;
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 12px;
  transition: all 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #0DFF00;
  background: rgba(255, 255, 255, 0.15);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-textarea {
  resize: vertical;
  min-height: 60px;
}

.form-select {
  cursor: pointer;
}

.form-select option {
  background: #2a2a2a;
  color: #FFFFFF;
}

/* 模式选择样式 */
.mode-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-top: 8px;
}

.mode-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.mode-option:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.mode-option.active {
  background: rgba(13, 255, 0, 0.1);
  border-color: #0DFF00;
}

.mode-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.mode-label {
  font-size: 12px;
  font-weight: 500;
  color: #FFFFFF;
  margin-bottom: 4px;
}

.mode-desc {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

/* 表单按钮 */
.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.submit-btn,
.reset-btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-btn {
  background: #0DFF00;
  color: #000000;
}

.submit-btn:hover {
  background: #0BCC00;
  transform: translateY(-1px);
}

.reset-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.reset-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 普通状态下的比例调整 - 保持与最大化状态相同的视觉比例 */
.parameter-panel.normal .panel-title {
  font-size: 16px; /* 保持合适的标题字体 */
}

.parameter-panel.normal .status-text {
  font-size: 11px; /* 按比例缩放 */
}

.parameter-panel.normal .custom-scrollbar {
  width: 6px; /* 按比例缩放 */
}

.parameter-panel.normal .form-group label {
  font-size: 12px; /* 按比例缩放 */
}

.parameter-panel.normal .form-input,
.parameter-panel.normal .form-textarea,
.parameter-panel.normal .form-select {
  font-size: 11px; /* 按比例缩放 */
  padding: 8px 10px; /* 按比例缩放 */
}

.parameter-panel.normal .mode-label {
  font-size: 11px; /* 按比例缩放 */
}

.parameter-panel.normal .mode-desc {
  font-size: 9px; /* 按比例缩放 */
}

.parameter-panel.normal .status-dot {
  width: 6px; /* 按比例缩放 */
  height: 6px;
}

.parameter-panel.normal .settings-button {
  width: 14px; /* 按比例缩放 */
  height: 14px;
}

.parameter-panel.normal .settings-button svg {
  width: 14px;
  height: 14px;
}
</style>
